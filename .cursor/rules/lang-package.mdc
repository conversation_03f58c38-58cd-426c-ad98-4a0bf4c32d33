---
description: lang包技术背景
globs:
alwaysApply: false
---
packages/lang实现了packages/core目录的核心功能，并且复用了工具调用的逻辑。lang包基于langchainjs和langgraph实现，并且提供了cli工具。
- 实现lang的逻辑时，请注意不要使用any、unknown等类型，而是使用具体的类型。
- lang包的编译命令： `npm run build --workspace=packages/lang` 或`cd packages/lang && npm run build`
- lang包的测试脚本: 
   1. 方式一：`node packages/lang/dist/cli.js --interactive --debug`，其中interactive 标识交互模式，debug 标识调试模式，测试时可选择使用
   2. 方式二：`GEMINI_CLI_USE_LANGCHAIN=true node packages/cli/dist/index.js -i`，通过packages/cli的交互模式测试lang包的逻辑
