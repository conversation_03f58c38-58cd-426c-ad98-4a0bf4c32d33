### LangGraph Checkpointer 与 Langfuse 接入说明

本说明覆盖：
- 自定义文件型 `BaseCheckpointSaver` 的使用与磁盘查看方式
- 官方持久化后端（Postgres/Mongo/SQLite）参考
- Langfuse 在 LangChain/LangGraph JS/TS 场景的接入与查看

---

### 1) 文件型 Checkpointer（已内置）

- 实现位置：`packages/lang/src/core/fileCheckpointSaver.ts`
- 导出路径：`packages/lang/src/index.ts` 中 `export { FileCheckpointSaver } ...`
- 用途：替换默认的内存 `MemorySaver`，将 LangGraph 的检查点与中间 writes 落盘，便于调试与人工查看。

使用示例：

```ts
import { StateGraphAgent, FileCheckpointSaver } from '@google/gemini-cli-lang';

const checkpointer = new FileCheckpointSaver();
const agent = new StateGraphAgent(config, '', { checkpointer, enablePersistence: true });

// 运行时传入 thread_id（通常复用会话ID）
const result = await agent.getGraph().invoke(initialState, {
  configurable: { thread_id: sessionId },
});
```

落盘目录结构（按当前项目路径哈希）：

```
~/.haicode/tmp/<项目哈希>/langgraph/<thread_id>/
  ├─ checkpoints/
  │   └─ checkpoint-<checkpoint_id>.json
  └─ writes/
      └─ writes-<taskId>.json
```

快速查看：
- 列表：`ls -la ~/.haicode/tmp/<hash>/langgraph/<thread_id>/checkpoints`
- 查看文件：`cat ~/.haicode/tmp/<hash>/langgraph/<thread_id>/checkpoints/checkpoint-<checkpoint_id>.json`

说明：`<hash>` 由项目绝对路径中的 `/` 替换为 `__` 后再 URL 编码得到（可参考 `packages/lang/src/utils/paths.ts`）。

---

### 2) 官方持久化后端（可选）

若需更强的持久化能力或多实例共享：

- Postgres：`@langchain/langgraph-checkpoint-postgres`
  ```ts
  import { PostgresSaver } from '@langchain/langgraph-checkpoint-postgres';
  const checkpointer = PostgresSaver.fromConnString('********************************/db', { schema: 'public' });
  await checkpointer.setup();
  const app = workflow.compile({ checkpointer });
  ```

- MongoDB：`@langchain/langgraph-checkpoint-mongodb`（参考官方 API 文档 `MongoDBSaver`）

- Sqlite/AsyncSqlite：LangGraph 官方提供（JS/TS 版本参考官方文档“Persistence/Checkpointing”）。

以上后端均与本项目 `StateGraphAgent` 的 `options.checkpointer` 参数兼容。

---

### 3) Langfuse 接入（LangChain/LangGraph JS/TS）

Langfuse 通过 LangChain 回调集成，支持 `invoke`/`stream` 等接口，自动采集链路与模型调用信息。

安装：
```bash
npm i langfuse-langchain
```

最小示例（LCEL/链式调用）：
```ts
import { CallbackHandler } from 'langfuse-langchain';
import { RunnableSequence } from '@langchain/core/runnables';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const langfuseHandler = new CallbackHandler({
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  baseUrl: process.env.LANGFUSE_BASEURL || 'https://cloud.langfuse.com',
});

const model = new ChatOpenAI({});
const prompt = PromptTemplate.fromTemplate('Tell me a joke about {topic}');
const chain = RunnableSequence.from([prompt, model]);

const res = await chain.invoke(
  { topic: 'bears' },
  { callbacks: [langfuseHandler] }
);
```

在 `StateGraphAgent` 中使用（两种姿势）：
- 直接给模型/链传 `callbacks`：
  ```ts
  await agent.getGraph().invoke(initialState, {
    configurable: { thread_id: sessionId },
    callbacks: [langfuseHandler],
  });
  ```
- 或在更高一层将 `CallbackHandler` 作用于你封装的 `Runnable`/链路。

动态设置 trace 属性（用户/会话/标签）：
```ts
await chain.invoke(
  { topic: 'cats' },
  {
    callbacks: [langfuseHandler],
    runName: 'my-trace',
    tags: ['prod'],
    metadata: { langfuseUserId: 'user-123', langfuseSessionId: 'sess-abc' },
  }
);
```

短进程/Serverless 下确保回调送达：
```ts
await langfuseHandler.flushAsync();
// 或 await langfuseHandler.shutdownAsync();
```

在 Langfuse Web UI 查看：
- 登录你配置的 `baseUrl`（EU: `https://cloud.langfuse.com` / US: `https://us.cloud.langfuse.com`）
- 进入项目后在 Traces 中按 `runName`/`sessionId`/`userId`/标签筛选

---

### 4) 与本项目会话持久化的关系

- `SessionManager`：管理人机对话消息与自定义 checkpoint（另存为 `checkpoint-<tag>.json`），位置：`~/.haicode/tmp/<hash>/sessions/<sessionId>/`。
- LangGraph Checkpointer（本文）：管理图状态的检查点（用于多轮/工具调用时的内部调度与续跑），默认内存 `MemorySaver`，可替换为上述文件型或数据库后端。
- Langfuse：用于可观测性/追踪（链路、模型、工具调用、指标），与持久化存储解耦。

三者互补：Session（语义历史）/LangGraph Checkpoint（执行快照）/Tracing（可观测）。


