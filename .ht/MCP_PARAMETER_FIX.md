# MCP工具参数信息修复

## 问题描述

在`packages/lang`中的MCP相关逻辑存在问题：**MCP服务工具的参数信息没有写入提示词中，只写了描述信息**。

### 具体问题

1. **缺少参数信息**：MCP工具的描述中没有包含参数的详细信息
2. **Schema不完整**：MCPToolAdapter没有正确暴露工具的参数schema给LangChain
3. **LLM无法了解参数**：由于缺少参数信息，LLM无法正确调用MCP工具

## 修复方案

### 1. 增强工具描述信息

修改`MCPToolAdapter`类的`generateEnhancedDescription()`方法，从MCP工具的`parameterSchemaJson`中提取参数信息并添加到描述中：

```typescript
private generateEnhancedDescription(): string {
  let description = this.mcpTool.description;

  try {
    const paramSchema = this.mcpTool.parameterSchemaJson as Record<string, unknown>;
    if (paramSchema && paramSchema.type === 'object' && paramSchema.properties) {
      description += '\n\nParameters:';
      const properties = paramSchema.properties as Record<string, Record<string, unknown>>;
      const required = (paramSchema.required as string[]) || [];
      
      for (const [paramName, paramDef] of Object.entries(properties)) {
        const isRequired = required.includes(paramName) ? ' (required)' : ' (optional)';
        const paramDesc = (paramDef.description as string) || 'No description';
        const paramType = (paramDef.type as string) || 'unknown';
        description += `\n- ${paramName} (${paramType})${isRequired}: ${paramDesc}`;
      }
    }
  } catch (error) {
    logger.debug(`[MCPToolAdapter] Failed to parse parameter schema for ${this.name}:`, error);
  }

  return description;
}
```

### 2. 修复Schema兼容性

确保`MCPToolAdapter`的schema属性与LangChain Tool基类兼容：

```typescript
private createLangChainSchema() {
  // 使用与CoreToolWrapper相同的模式以确保兼容性
  return z.object({
    input: z.string().optional().describe('Tool input parameters as JSON string')
  }).transform((val) => val.input || '');
}
```

### 3. 改进参数处理

优化`_call`方法以正确处理JSON参数：

```typescript
async _call(input: string): Promise<string> {
  try {
    // 解析输入参数
    let args: Record<string, unknown>;
    try {
      args = JSON.parse(input);
    } catch {
      // 如果不是JSON，作为简单字符串输入处理
      args = { input };
    }

    // 执行MCP工具
    const result = await this.mcpTool.execute(args);
    
    // 格式化结果
    if (typeof result.returnDisplay === 'string') {
      return result.returnDisplay;
    } else if (result.returnDisplay && typeof result.returnDisplay === 'object') {
      return JSON.stringify(result.returnDisplay);
    }
    
    return result.returnDisplay ? String(result.returnDisplay) : 'No output';
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`[MCPToolAdapter] Error executing MCP tool ${this.name}:`, error);
    return `Error executing MCP tool ${this.name}: ${errorMessage}`;
  }
}
```

## 修复效果

### 修复前
```
📝 描述:
This MCP tool enables users to perform targeted documentation searches for the @ht/sprite-ui npm package...
```

### 修复后
```
📝 描述:
This MCP tool enables users to perform targeted documentation searches for the @ht/sprite-ui npm package...

Parameters:
- text (string) (required): user input
```

## 测试验证

### 测试1：参数信息显示
```bash
node test-mcp-params.js
```

**结果**：✅ 成功显示参数信息
- 工具描述包含详细的参数信息
- 参数类型、是否必需、描述都正确显示

### 测试2：工具发现和注册
```bash
node test-mcp-prompt.js
```

**结果**：✅ 工具正常发现和注册
- 发现了12个工具，其中1个MCP工具
- MCP工具正确注册到LangChain工具系统

## 技术细节

### 修改文件
- `packages/lang/src/mcp/mcpClient.ts`

### 关键改进
1. **类型安全**：移除了`any`类型，使用更具体的类型定义
2. **错误处理**：增强了错误处理和日志记录
3. **兼容性**：确保与LangChain Tool基类完全兼容
4. **信息完整性**：工具描述现在包含完整的参数信息

### 依赖关系
- 依赖`@google/gemini-cli-core`中的`DiscoveredMCPTool`
- 兼容LangChain的Tool基类
- 使用Zod进行schema定义

## 预期效果

修复后，LLM将能够：
1. **了解工具参数**：从工具描述中获取参数信息
2. **正确调用工具**：基于参数信息生成正确的工具调用
3. **提供更好的用户体验**：更准确地使用MCP工具

这个修复确保了MCP工具的参数信息能够正确传递给LLM，解决了原有的参数信息缺失问题。
