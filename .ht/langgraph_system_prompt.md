### 在 LangGraph.js 中使用 StateGraph 创建 Agent 的系统提示词传递与工具处理

基于 LangGraph.js 官方文档、LangChain.js 集成指南以及相关教程的最佳实践，以下是针对使用 StateGraph 构建自定义 agent、DeepSeek-V3 模型的详细说明。信息主要来源于 LangGraph.js how-to 指南、LangChain.js API 文档和相关教程（如 DataCamp 的 LangGraph 教程）。DeepSeek-V3 通过 `@langchain/deepseek` 包集成，支持系统提示和工具调用（兼容 OpenAI 格式）。如果模型是 "deepseek-v3"（假设为 DeepSeek-V3 或类似），它支持工具调用，但某些变体如 "deepseek-reasoner" 可能有限制（截至 2025 年 1 月）。

#### 1. **传递系统提示词（System Prompt）**
   - **在 StateGraph 中的处理**：StateGraph 是 LangGraph.js 的核心，用于构建自定义状态图和 agent。没有内置的 "系统提示" 参数（如 prebuilt ReAct agent 的 `stateModifier`），但你可以在 agent 节点函数中显式构建提示消息列表，包括 `SystemMessage`。这允许自定义逻辑，将系统提示作为消息的一部分传递给模型。
     - **最佳实践**：使用 `ChatPromptTemplate` 或直接消息数组来管理提示。保持系统提示简洁、具体（如定义角色或响应风格），并动态注入状态（如用户信息）。测试时，确保模型（如 DeepSeek-V3）支持系统消息（它通过消息数组支持）。避免硬编码提示，使用函数形式动态生成以适应状态变化。

   **代码示例（自定义 StateGraph agent）**：
   ```javascript
   import { StateGraph } from "@langchain/langgraph";
   import { ChatDeepSeek } from "@langchain/deepseek";
   import { AIMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";
   import { ChatPromptTemplate } from "@langchain/core/prompts";

   // 定义状态（通常包括 messages）
   const State = {
     messages: {
       value: (x, y) => x.concat(y),
       default: () => [],
     },
   };

   // 初始化模型
   const model = new ChatDeepSeek({ model: "deepseek-v3", temperature: 0 });

   // 创建图
   const graph = new StateGraph(State);

   // 添加 agent 节点
   graph.addNode("agent", async (state) => {
     // 系统提示词
     const systemPrompt = "You are a helpful assistant. Respond in a structured format.";
     
     // 使用 ChatPromptTemplate 构建提示（可选）
     const promptTemplate = ChatPromptTemplate.fromMessages([
       ["system", systemPrompt],
       ...state.messages,  // 包含历史消息
     ]);
     
     // 或直接消息数组
     const messages = [
       new SystemMessage(systemPrompt),
       ...state.messages,
     ];

     const response = await model.invoke(await promptTemplate.formatMessages({}));  // 或直接 invoke(messages)
     return { messages: [response] };
   });

   // 添加边和编译图（根据需要添加工具节点等）
   graph.addEdge("__start__", "agent");
   const app = graph.compile();

   // 调用
   const result = await app.invoke({ messages: [new HumanMessage("Hello!")] });
   ```
   - **DeepSeek-V3 特定配置**：使用 `ChatDeepSeek` 类初始化，确保 API 密钥设置（`process.env.DEEPSEEK_API_KEY`）。系统提示通过消息数组的 "system" 角色传递，支持 chaining（如与 `pipe` 结合）。如果需要动态提示，使用函数形式注入变量。

#### 2. **处理声明的工具的提示词**
   - **工具是否自动写入提示**：在 StateGraph 自定义 agent 中，工具不会自动写入系统提示，但通过 `model.bindTools(tools)` 绑定后，工具的 schema（名称、描述、参数）会隐式包含在模型调用中。模型（如 DeepSeek-V3）会根据输入决定是否调用工具，并在响应中返回 `tool_calls`。
     - 如果不自动：需要在 agent 节点中手动绑定工具，并在系统提示中显式提及工具可用性（如 "Use tools for calculations."）。对于 ReAct 风格的图，使用 `ToolNode` 处理工具执行，并更新状态。
     - **最佳实践**：工具描述应简洁、具体（使用 Zod schema 定义），并在系统提示中指导模型使用工具。限制工具数量以避免混淆。测试工具调用链，确保模型支持（如 DeepSeek-V3 的 OpenAI 兼容性）。在自定义图中，使用 `stateModifier` 函数动态更新提示基于工具输出。

   **代码示例（添加工具到 StateGraph）**：
   ```javascript
   import { tool } from "@langchain/core/tools";
   import { z } from "zod";
   import { ToolNode } from "@langchain/langgraph/prebuilt";  // 用于工具执行节点

   // 定义工具
   const getWeather = tool(async ({ location }) => {
     return `Weather in ${location}: Sunny`;  // 模拟
   }, {
     name: "get_weather",
     description: "Get current weather for a location.",  // 此描述会影响模型决策
     schema: z.object({ location: z.string() }),
   });

   const tools = [getWeather];

   // 在 agent 节点中绑定工具
   graph.addNode("agent", async (state) => {
     const systemPrompt = "You are a weather assistant. Use tools if needed.";
     const modelWithTools = model.bindTools(tools);  // 绑定工具 schema
     
     const messages = [
       new SystemMessage(systemPrompt + " Available tools: get_weather."),
       ...state.messages,
     ];

     const response = await modelWithTools.invoke(messages);
     return { messages: [response] };
   });

   // 添加工具节点
   const toolNode = new ToolNode(tools);
   graph.addNode("tools", toolNode);

   // 添加条件边（例如，如果有 tool_calls，则去 tools 节点）
   graph.addConditionalEdges("agent", (state) => {
     const lastMsg = state.messages[state.messages.length - 1];
     return lastMsg.tool_calls?.length ? "tools" : "__end__";
   });
   graph.addEdge("tools", "agent");  // 循环回 agent
   ```
   - **DeepSeek-V3 特定**：支持工具调用，通过 `bindTools` 工作。响应中检查 `tool_calls` 数组。如果工具输出需更新状态，使用 `Command` 对象（如在工具返回中指定更新键）。

#### 3. **比较总结（表格形式）**
| 方面              | 系统提示词处理                              | 工具提示词处理                              |
|-------------------|---------------------------------------------|---------------------------------------------|
| **StateGraph 自定义 Agent** | 在节点中构建消息列表或使用 ChatPromptTemplate，包括 SystemMessage。动态函数最佳。 | 通过 bindTools() 绑定 schema；手动在系统提示中提及工具。使用 ToolNode 执行。 |
| **DeepSeek-V3 集成** | 通过消息数组的 "system" 角色；支持 chaining。 | 支持工具调用（OpenAI 兼容）；检查 tool_calls。某些变体可能需验证。 |
| **最佳实践**     | 简洁、角色导向；测试动态注入。             | 清描述、少工具；更新状态以反馈提示。       |

总体建议：从 prebuilt ReAct agent 迁移到 StateGraph 以获得更多控制，但从简单图开始测试。参考官方 API 文档（如 https://api.js.langchain.com/）获取最新更新。