### packages/lang README 更新要点（2025-08-08）

- 同步 API 与实现：
  - 将文档中的 `createLangChainGeminiCLI` 更正为 `createHaicodeAgent`，与 `src/index.ts` 实现一致。
  - Agent 命名从 `LangChainAgent` 更正为 `StateGraphAgent`，与 `core/stateGraphAgent.ts` 一致。
  - 增补 `cli-integration.ts` 的使用说明（`createCLIInstance`、`GEMINI_CLI_USE_LANGCHAIN`）。

- 模型与鉴权：
  - 补充多供应商（Gemini/Vertex、OpenAI-Compatible、Anthropic）与 `ExtendedAuthType` 用法示例。
  - 增加 `createChatModel`/`createEmbeddings` 的 OpenAI-Compatible 基本示例与可选 `baseURL` 参数。
  - 说明 `getDefaultModel` 与 `validateModelCompatibility` 的用途。

- 会话管理与持久化：
  - 明确 `SessionManager` 的本地持久化路径：`~/.haicode/tmp/<project-hash>/sessions`。
  - 说明支持导入/导出与 checkpoint（与 `HaicodeLogger`、LangGraph checkpointer 配合）。

- 可选集成：
  - 增补 **Langfuse**（`langfuse-langchain`，配置于 `src/config/langfuse.ts`）。
  - 增补 **MongoDB Checkpointer**（`@langchain/langgraph-checkpoint-mongodb`，配置于 `src/config/mongo.ts`，创建时传 `checkpointer: true`）。

- CLI：
  - 同步 `hai-code` 的参数与环境变量（`-m/-b/-i/-d`，以及 `OPENAI_BASE_URL` 等）。
  - 增加示例命令（本地/代理 OpenAI-Compatible，默认模型为 `ht::saas-deepseek-v3`）。

- 架构图：
  - 更新目录结构，加入 `cli-integration.ts`、`config/langfuse.ts`、`config/mongo.ts`、`core/stateGraphAgent.ts` 等文件。

注意：`src/config/langfuse.ts` 与 `src/config/mongo.ts` 当前包含测试环境示例配置，生产使用请改为从环境变量加载。

