# packages/lang 工具调用参数解析修复分析

## 问题分析

### 核心问题

在 `packages/lang` 中的 `mapStringToParams` 方法存在一个严重的参数映射问题，会导致生成错误的参数格式如 `{ import: 'xxx' }`，从而导致工具调用失败。

### 问题根源

**原始代码问题**（`packages/lang/src/tools/toolRegistry.ts` 第159-200行）：

```typescript
// 当没有required参数时，使用第一个property
if (parameters.properties && typeof parameters.properties === 'object') {
    const propertyNames = Object.keys(parameters.properties);
    if (propertyNames.length > 0) {
        primaryParamName = propertyNames[0]; // 🚨 问题：可能是 'import'
    }
}
```

**问题场景**：
1. 当工具schema中第一个属性恰好是 `import`、`export`、`default` 等JavaScript保留字时
2. `mapStringToParams` 会生成 `{ import: 'xxx' }` 这样的参数
3. 但实际工具期望的是正确的参数名，如 `{ pattern: 'xxx' }` 或 `{ path: 'xxx' }`

### packages/core vs packages/lang 差异

| 方面 | packages/core | packages/lang |
|------|---------------|---------------|
| **参数处理** | 直接接收正确格式的参数 | 需要将字符串映射为参数对象 |
| **工具调用** | 直接调用 `tool.execute(params)` | 通过 `CoreToolWrapper` 适配 |
| **参数验证** | 在工具内部进行 | 在wrapper中预处理后再验证 |
| **错误来源** | 参数格式错误直接暴露 | 可能在映射阶段产生错误参数 |

## 修复方案

### 1. 预定义参数映射表

为所有已知的core工具创建明确的参数映射：

```typescript
const toolParamMapping: Record<string, string> = {
    'list_directory': 'path',
    'read_file': 'absolute_path', 
    'search_file_content': 'pattern',
    'glob': 'pattern',
    'replace': 'old_str',
    'write_file': 'file_path',
    'web_fetch': 'url',
    'read_many_files': 'paths',
    'run_shell_command': 'command',
    'save_memory': 'content',
    'web_search': 'query'
};
```

### 2. 安全的fallback机制

对于未知工具，过滤掉危险的参数名：

```typescript
// 避免JavaScript保留字和危险的参数名
const safePropertyNames = propertyNames.filter(name => 
    !['import', 'export', 'default', 'class', 'function'].includes(name)
);
```

### 3. 增强的hackUtils处理

为每个工具添加专门的参数转换逻辑，确保 `input` 参数被正确映射：

```typescript
function hackGlob(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.pattern = args.input;
    delete args.input; // 🔧 删除input避免混淆
  }
  return args;
}
```

## 修复效果验证

### 测试结果

运行 `node test-mapStringToParams.cjs` 的结果：

```
📊 测试结果: 6/6 通过
🎉 所有测试通过！mapStringToParams修复成功！

🚨 特殊测试：检查是否还会产生 { import: "xxx" } 问题
✅ { import: "xxx" } 问题已修复！
```

### 修复前后对比

| 场景 | 修复前 | 修复后 |
|------|--------|--------|
| **list_directory** | `{ import: './' }` ❌ | `{ path: './' }` ✅ |
| **glob** | `{ import: '**/*.ts' }` ❌ | `{ pattern: '**/*.ts' }` ✅ |
| **read_file** | `{ import: '/path/file.txt' }` ❌ | `{ absolute_path: '/path/file.txt' }` ✅ |
| **未知工具** | `{ import: 'value' }` ❌ | `{ safe_param: 'value' }` ✅ |

## 技术细节

### 修复的关键点

1. **预定义映射优先**：对已知工具使用明确的参数映射，避免动态推断错误
2. **安全过滤**：对未知工具过滤掉JavaScript保留字
3. **类型处理**：正确处理数组类型参数（如 `read_many_files`）
4. **参数清理**：在hackUtils中删除 `input` 参数避免混淆

### 兼容性保证

- ✅ **向后兼容**：现有的正确工具调用不受影响
- ✅ **错误处理**：保持完整的错误处理和日志记录
- ✅ **类型安全**：维持TypeScript类型检查

## 相关文件修改

### 主要修改

1. **`packages/lang/src/tools/toolRegistry.ts`**
   - 重写 `mapStringToParams` 方法
   - 添加预定义工具参数映射
   - 增强安全过滤机制

2. **`packages/lang/src/tools/hackUtils.ts`**
   - 为所有core工具添加参数转换逻辑
   - 确保 `input` 参数被正确映射和清理
   - 处理相对路径到绝对路径的转换

### 测试文件

- **`test-mapStringToParams.cjs`**：单元测试验证修复效果
- **`test-parameter-fix.js`**：集成测试验证实际工具调用

## 总结

这次修复解决了 `packages/lang` 中一个关键的参数映射问题：

1. **问题根源**：动态参数推断可能选择错误的参数名（如 `import`）
2. **修复策略**：预定义映射 + 安全过滤 + 参数清理
3. **验证结果**：所有测试通过，`{ import: 'xxx' }` 问题完全修复
4. **影响范围**：提升了所有core工具在lang包中的调用可靠性

修复后，`packages/lang` 的工具调用参数解析变得更加健壮和可预测，确保了与 `packages/core` 的完美兼容性。