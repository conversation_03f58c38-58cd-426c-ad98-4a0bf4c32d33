# Lang包工具调用参数修复报告

## 问题描述

用户在使用 `node packages/lang/dist/cli.js --interactive --debug` 测试 lang 包功能时，输出日志显示工具调用参数总是出错。具体错误包括：

### 错误现象
1. `Tool validation failed: params must have required property 'path'` (对于 list_directory)
2. `Tool validation failed: params must have required property 'pattern'` (对于 glob)
3. `Input is required but was undefined` (部分调用)

### 错误日志示例
```
[CoreToolWrapper] Input is not JSON, treating as string: ./
[CoreToolWrapper] Error executing list_directory: Error: Tool validation failed: params must have required property 'path'
```

## 根源分析

问题出现在 `packages/lang/src/tools/toolRegistry.ts` 文件的 `CoreToolWrapper._call` 方法中：

### 原始问题代码
```typescript
// 原来的逻辑
catch (_parseError) {
    logger.debug(`[CoreToolWrapper] Input is not JSON, treating as string: ${input}`);
    args = { input };  // ❌ 错误：所有工具都用 'input' 参数名
}
```

### 问题根源
1. 当 LangChain 传递字符串参数给工具时，代码尝试将其解析为 JSON
2. 如果不是 JSON，代码将字符串包装为 `{ input: "string" }`
3. 但各个 core tools 期望的参数名不同：
   - `list_directory` 期望 `path` 参数
   - `glob` 期望 `pattern` 参数
   - `run_shell_command` 期望 `command` 参数
   - 等等...

## 解决方案

### 修复策略（V2.0 - 基于 Schema 的动态解析）
1. **动态解析工具 Schema**: 直接从 `coreTool.schema` 中提取参数信息
2. **自动参数检测**: 优先使用第一个必需参数，无必需参数时使用第一个属性
3. **类型智能检测**: 自动检测参数类型，对数组类型参数进行包装
4. **与 core 包保持同步**: 无需硬编码映射，自动适应 core 包变化

### 修复代码（V2.0 版本）

```typescript
/**
 * Map string input to correct parameter object based on core tool schema
 * This dynamically extracts parameter info from the tool's schema definition
 */
private mapStringToParams(input: string): Record<string, string | string[]> {
    try {
        const schema = this.coreTool.schema;
        const parameters = schema.parameters;

        if (!parameters || typeof parameters !== 'object') {
            logger.warning(`[CoreToolWrapper] No parameters schema found for tool: ${this.name}`);
            return { input };
        }

        // First, try to get the first required parameter
        let primaryParamName: string | undefined;
        if (parameters.required && Array.isArray(parameters.required) && parameters.required.length > 0) {
            primaryParamName = parameters.required[0];
        } else if (parameters.properties && typeof parameters.properties === 'object') {
            // If no required parameters, use the first property
            const propertyNames = Object.keys(parameters.properties);
            if (propertyNames.length > 0) {
                primaryParamName = propertyNames[0];
            }
        }

        if (!primaryParamName) {
            logger.warning(`[CoreToolWrapper] No suitable parameter found in schema for tool: ${this.name}`);
            return { input };
        }

        // Check parameter type to determine if we need to wrap in array
        const paramProperty = parameters.properties?.[primaryParamName];
        if (paramProperty && typeof paramProperty === 'object') {
            // Check if this parameter expects an array
            if (paramProperty.type === Type.ARRAY || 
                (typeof paramProperty.items === 'object' && paramProperty.items !== null)) {
                logger.debug(`[CoreToolWrapper] Parameter '${primaryParamName}' expects array, wrapping input`);
                return { [primaryParamName]: [input] };
            }
        }

        // Default case: single string parameter
        logger.debug(`[CoreToolWrapper] Mapping string input to parameter '${primaryParamName}' for tool: ${this.name}`);
        return { [primaryParamName]: input };

    } catch (error) {
        logger.error(`[CoreToolWrapper] Error extracting schema info for tool ${this.name}:`, error);
        // Fallback to generic parameter
        return { input };
    }
}
```

### 修改后的调用逻辑
```typescript
catch (_parseError) {
    // If not JSON, map the string to the correct parameter name based on tool
    logger.debug(`[CoreToolWrapper] Input is not JSON, treating as string: ${input}`);
    args = this.mapStringToParams(input);  // ✅ 修复：根据工具类型映射参数
}
```

## 技术细节

### V2.0 版本优势
1. **自动同步**: 与 `@google/gemini-cli-core` 包完全同步，无需手动维护映射表
2. **智能检测**: 基于工具 schema 自动检测参数名称和类型
3. **类型安全**: 使用 `Type.ARRAY` 枚举而不是字符串比较
4. **容错性强**: 多层降级机制，确保即使 schema 异常也能正常工作

### Schema 解析逻辑
1. **优先级策略**: 
   - 首选：`schema.parameters.required[0]` (第一个必需参数)
   - 次选：`Object.keys(schema.parameters.properties)[0]` (第一个属性)
   - 后备：通用 `input` 参数

2. **类型检测**:
   - 检查 `paramProperty.type === Type.ARRAY`
   - 检查是否存在 `paramProperty.items` 对象
   - 数组类型参数自动包装为 `[input]`

### 导入依赖
```typescript
import { Type } from '@google/genai';
import type {
    Tool as CoreTool,
    ToolResult,
} from '@google/gemini-cli-core';
```

## 验证方法

1. 重新编译 lang 包：
   ```bash
   cd packages/lang && npm run build
   ```

2. 运行测试：
   ```bash
   node packages/lang/dist/cli.js --interactive --debug
   ```

3. 输入测试命令，如"解释这个项目"，验证工具调用是否成功

## 预期效果

修复后，工具调用应该正常工作，不再出现参数验证错误，能够正确执行：
- 目录列表操作
- 文件搜索操作  
- 其他各种工具功能

## V2.0 版本更新说明

**用户建议优化**: 直接使用 `@google/gemini-cli-core` 中的 Tool schema 逻辑，避免硬编码映射。

**优化后的优势**:
1. **零维护**: 当 core 包工具发生变化时，lang 包自动适应
2. **类型安全**: 使用正确的枚举类型而不是字符串
3. **扩展性**: 新增工具无需修改 lang 包代码
4. **一致性**: 完全遵循 core 包的 schema 定义

## 注意事项

1. ✅ 使用具体类型 `Record<string, string | string[]>` 而不是 `any`
2. ✅ 完全依赖 core 包的 schema 定义，保持一致性  
3. ✅ 自动适应未来新增工具，无需手动更新映射
4. ✅ 多层错误处理和降级机制，提高健壮性

---
*初始修复完成时间：2025年1月27日*
*V2.0 版本优化完成时间：2025年1月27日*
*修复人员：AI助手*