# Lang 包改进计划：利用 LangChain/LangGraph 成熟 API

## 执行日期
2025年1月27日

## 改进策略概述

基于对 LangChain 和 LangGraph 成熟 API 的调研，我们可以显著简化 lang 包的实现，减少代码量，同时提供更强大和可靠的功能。

## 1. 利用 LangGraph 成熟特性

### 1.1 使用 StateGraph 和内置节点

**当前实现：** 自定义 `LangChainAgent` 类
**建议改进：** 直接使用 LangGraph 的 `StateGraph` 和预构建节点

```typescript
// 当前实现（简化版）
export class LangChainAgent {
  async processMessage(userMessage: string, sessionId: string): Promise<string> {
    let maxIterations = 5; // 简单循环限制
    while (response.tool_calls?.length && maxIterations > 0) {
      // 手动处理工具调用循环
      maxIterations--;
    }
  }
}

// 建议改进：使用 LangGraph StateGraph
import { StateGraph, ToolNode } from 'langgraph';

const workflow = new StateGraph(AgentState);

// 使用预构建的 ToolNode
const toolNode = new ToolNode(tools);
workflow.addNode("agent", callModel);
workflow.addNode("action", toolNode);

// 使用条件边控制流程
workflow.addConditionalEdges(
  "agent",
  shouldContinue,
  {
    "continue": "action",
    "end": END,
  }
);

// 内置递归限制和错误处理
const app = workflow.compile({
  checkpointer: memory,
  recursionLimit: 25 // LangGraph 内置递归限制
});
```

### 1.2 利用内置的循环检测和限制

**优势：**
- LangGraph 有内置的 `recursion_limit` 参数
- 支持动态调整递归限制
- 提供 `GRAPH_RECURSION_LIMIT` 错误处理
- 支持人工介入后重置限制

```typescript
// 内置递归限制配置
app.invoke(input, { 
  recursionLimit: 100,
  configurable: { 
    threadId: "session-1" 
  }
});

// 人工介入时重置限制
app.updateState(config, newState, {
  recursionLimit: 50  // 重新设置限制
});
```

### 1.3 使用 Checkpointer 进行状态管理

**当前实现：** 自定义 `SessionManager`
**建议改进：** 使用 LangGraph 内置 checkpointer

```typescript
// 当前实现
export class SessionManager {
  private sessions: Map<string, SessionData> = new Map();
  // 大量自定义会话管理代码...
}

// 建议改进：使用内置 checkpointer
import { SqliteSaver } from 'langgraph/checkpoint/sqlite';

const memory = SqliteSaver.fromConnString(":memory:");
const app = workflow.compile({ checkpointer: memory });

// 自动获得：
// - 会话持久化
// - 状态回滚
// - 时间旅行功能
// - 分支和版本控制
```

## 2. MCP 支持集成策略

### 2.1 LangGraph 的工具集成模式

```typescript
// 创建 MCP 工具适配器
class MCPToolAdapter extends Tool {
  constructor(private mcpClient: MCPClient, private toolName: string) {
    super();
    this.name = toolName;
  }

  async _call(input: string): Promise<string> {
    return this.mcpClient.callTool({
      name: this.toolName,
      args: JSON.parse(input)
    });
  }
}

// 集成到 LangGraph workflow
const mcpTools = await discoverMCPTools();
const allTools = [...coreTools, ...mcpTools];
const toolNode = new ToolNode(allTools);

workflow.addNode("tools", toolNode);
```

### 2.2 简化的 MCP 客户端实现

```typescript
// 轻量级 MCP 客户端，复用 core 包逻辑
export class LangChainMCPClient {
  constructor(private coreConfig: CoreConfig) {
    // 委托给 core 包的 MCP 实现
    this.mcpServers = this.coreConfig.getMcpServers();
  }

  async discoverTools(): Promise<Tool[]> {
    const coreToolRegistry = await import('@google/gemini-cli-core');
    const mcpTools = await coreToolRegistry.discoverMcpTools(
      this.mcpServers,
      this.coreConfig.getMcpServerCommand(),
      // 使用适配器包装为 LangChain 工具
      new LangChainToolRegistryAdapter()
    );
    return mcpTools;
  }
}
```

## 3. 高级特性利用

### 3.1 流式响应和中断

```typescript
// 利用 LangGraph 的内置流式和中断功能
const appWithInterrupts = workflow.compile({
  checkpointer: memory,
  interruptBefore: ["action"]  // 工具执行前中断
});

// 流式处理
for await (const event of appWithInterrupts.stream(input, config)) {
  // 实时处理事件
  if (event.type === "interrupt") {
    // 人工确认工具执行
    const approval = await getUserApproval(event.value);
    if (approval) {
      // 继续执行
      await appWithInterrupts.stream(null, config);
    }
  }
}
```

### 3.2 并行工具执行

```typescript
// 利用 LangGraph 的并行执行能力
async function parallelToolExecution(state: AgentState) {
  const toolCalls = state.messages[-1].tool_calls;
  
  // LangGraph 自动处理并行执行
  const toolNode = new ToolNode(tools);
  return toolNode.invoke(state);
}
```

### 3.3 错误处理和重试

```typescript
// 使用 LangGraph 的错误处理模式
function errorHandler(state: AgentState) {
  const lastMessage = state.messages[-1];
  if (lastMessage.type === "error") {
    // 自动重试逻辑
    return { ...state, retryCount: (state.retryCount || 0) + 1 };
  }
  return state;
}

workflow.addNode("error_handler", errorHandler);
workflow.addConditionalEdges(
  "tools",
  (state) => state.error ? "error_handler" : "agent"
);
```

## 4. 代码减少估算

### 4.1 当前代码行数对比

| 文件 | 当前行数 | 预期减少 | 减少后行数 |
|------|----------|----------|------------|
| `agent.ts` | 242 | 60% | ~97 |
| `sessionManager.ts` | 390 | 80% | ~78 |
| `toolRegistry.ts` | 262 | 40% | ~157 |
| `config.ts` | 482 | 30% | ~337 |
| **总计** | **1376** | **55%** | **~669** |

### 4.2 新增功能（无额外代码）

通过使用 LangGraph API，我们可以免费获得：

1. **循环检测** - 内置递归限制和检测
2. **状态管理** - checkpointer 提供完整持久化
3. **错误恢复** - 内置错误处理模式
4. **流式处理** - 原生流式支持
5. **并行执行** - 自动并行工具调用
6. **人工介入** - 内置中断和恢复机制

## 5. 实施路线图

### 阶段 1：核心重构（1-2周）

1. **替换 LangChainAgent**
   - 使用 StateGraph 替代自定义 agent 类
   - 集成 ToolNode 处理工具调用
   - 实现条件边和流程控制

2. **简化 SessionManager**
   - 使用 LangGraph checkpointer
   - 移除自定义会话管理代码
   - 保留必要的适配逻辑

### 阶段 2：MCP 集成（1周）

1. **MCP 工具适配**
   - 创建 MCPToolAdapter 类
   - 集成到 ToolNode
   - 复用 core 包的 MCP 发现逻辑

2. **测试和验证**
   - 验证 MCP 服务器连接
   - 测试工具调用功能
   - 确保与 core 包兼容

### 阶段 3：高级特性（1周）

1. **错误处理增强**
   - 实现重试机制
   - 添加错误恢复节点
   - 集成循环检测

2. **性能优化**
   - 启用并行工具执行
   - 优化状态更新
   - 添加监控和日志

## 6. API 兼容性保证

### 6.1 保持现有接口

```typescript
// 保持现有的创建函数接口
export async function createLangChainGeminiCLI(params: ConfigParameters) {
  // 内部使用 LangGraph，但保持外部接口不变
  const workflow = createStateGraph(params);
  const app = workflow.compile(getCheckpointerConfig(params));
  
  return {
    config: new LangChainConfigAdapter(params),
    sessionManager: new CheckpointerAdapter(app),
    agent: new StateGraphAdapter(app),
    
    // 保持现有的便捷方法
    async processMessage(userMessage: string, sessionId?: string): Promise<string> {
      const result = await app.invoke(
        { messages: [new HumanMessage(userMessage)] },
        { configurable: { threadId: sessionId || uuidv4() } }
      );
      return result.messages[-1].content;
    },
    
    async *streamMessage(userMessage: string, sessionId?: string): AsyncGenerator<string> {
      const stream = app.stream(
        { messages: [new HumanMessage(userMessage)] },
        { configurable: { threadId: sessionId || uuidv4() } }
      );
      
      for await (const chunk of stream) {
        if (chunk.messages?.length) {
          yield chunk.messages[-1].content;
        }
      }
    }
  };
}
```

### 6.2 向后兼容性

- 保持所有现有的 API 接口
- 新功能通过可选参数添加
- 渐进式迁移，支持混合使用

## 7. 预期收益

### 7.1 代码质量提升

- **减少 55% 的自定义代码**
- **零成本获得高级特性**
- **更好的测试覆盖率**（LangGraph 已经过充分测试）
- **更少的维护负担**

### 7.2 功能增强

- **内置循环检测** - 生产级别的安全保护
- **完整的状态管理** - 持久化、回滚、分支
- **人工介入支持** - 无缝的 HITL 集成
- **并行执行** - 更高的性能
- **错误恢复** - 更强的鲁棒性

### 7.3 开发体验

- **更少的样板代码**
- **更清晰的流程定义**
- **更好的调试支持**
- **丰富的文档和示例**

## 8. 风险评估与缓解

### 8.1 主要风险

1. **学习曲线** - 团队需要学习 LangGraph API
   - *缓解*：LangGraph 文档丰富，社区活跃

2. **依赖变更** - LangGraph API 可能变化
   - *缓解*：使用适配器模式，隔离外部依赖

3. **性能差异** - 可能与现有实现有性能差异
   - *缓解*：基准测试和性能监控

### 8.2 成功指标

- [ ] 代码行数减少 50%+
- [ ] MCP 支持完全实现
- [ ] 所有现有测试通过
- [ ] 性能保持或提升
- [ ] 增加高级特性（循环检测、HITL等）

## 结论

通过充分利用 LangChain 和 LangGraph 的成熟 API，我们可以显著简化 lang 包的实现，同时获得更强大的功能。这种方法不仅减少了代码量和维护负担，还提供了生产级别的可靠性和丰富的特性。

建议立即开始实施，优先完成核心重构，然后逐步添加高级特性。这种渐进式的方法可以最小化风险，同时快速获得收益。

---

**制定人员：** Claude AI Assistant  
**审查状态：** 待技术团队评审  
**预期完成时间：** 2-4周