# sessionId 与 thread_id 统一改造说明

本次改造目标：统一会话标识生成与传递逻辑，使之符合 LangChain/LangGraph 官方最佳实践。

## 关键变更

- 生成方式统一为 `crypto.randomUUID()`：
  - `packages/lang/src/core/sessionManager.ts`：`uuidv4()` → `randomUUID()`。
  - `packages/lang/src/cli.ts`：`Date.now()` 拼接生成的会话 ID → 使用 `randomUUID()` 生成。

- 线程标识传递：
  - 继续在 LangGraph 执行时通过 `RunnableConfig.configurable.thread_id` 传递（原实现已正确）。

## 受影响文件

- `packages/lang/src/core/sessionManager.ts`
- `packages/lang/src/cli.ts`

## 背景与原因

- LangGraph 的持久化（如 `MemorySaver`、MongoDB Saver）需要稳定一致的 `thread_id`。
- 官方推荐将会话标识通过 `RunnableConfig.configurable.thread_id` 传入，同时使用安全、不可预测的 ID 生成方法（Node 20+ 可用 `crypto.randomUUID()`）。

## 构建状态

- 已执行 `npm run build:packages`，构建通过。


