# Tiktoken "Unknown Model" 错误修复总结

## 问题描述

在使用自定义模型名称（如 `ht::saas-deepseek-r1`）时，Lang<PERSON>hai<PERSON> 的 `ChatOpenAI` 类会尝试使用 `js-tiktoken` 库来计算 token 数量。但是 `js-tiktoken` 只认识标准的 OpenAI 模型名称，当遇到未知模型名称时会抛出 "Unknown model" 错误。

### 错误信息
```
Failed to calculate number of tokens, falling back to approximate count Error: Unknown model
at getEncodingNameForModel (file:///Users/<USER>/projs/github/gemini-cli/node_modules/js-tiktoken/dist/chunk-ZDNLBERF.js:273:13)
```

## 修复方案

### 1. 模型工厂修复 (`packages/lang/src/core/modelFactory.ts`)

在 `createChatModel` 函数的 `USE_OPENAI_COMPATIBLE` 分支中，我们重写了 ChatOpenAI 实例的 token 计算方法：

```typescript
// 映射自定义模型名称到已知的 tiktoken 模型
const getTokenCountingModel = (modelName: string): string => {
  if (modelName.includes('deepseek') || modelName.includes('r1')) {
    return 'gpt-4'; // 使用 GPT-4 tokenizer 处理 DeepSeek 模型
  }
  if (modelName.includes('claude')) {
    return 'gpt-4'; // 使用 GPT-4 tokenizer 处理 Claude 类模型
  }
  if (modelName.includes('gemini')) {
    return 'gpt-3.5-turbo'; // 使用 GPT-3.5 tokenizer 处理 Gemini 类模型
  }
  return 'gpt-3.5-turbo'; // 未知模型的默认选择
};
```

### 2. 重写的方法

- **`getNumTokens`**: 临时替换模型名称进行 token 计算，失败时使用字符数估算
- **`getNumTokensFromMessages`**: 处理消息数组的 token 计算，返回正确的格式
- **字符数估算**: 作为最终的回退方案（1 token ≈ 4 字符）

### 3. 性能优化器改进 (`packages/lang/src/core/performanceOptimizer.ts`)

改进了 `estimateTokens` 方法，使其更加健壮：

```typescript
private estimateTokens(messages: BaseMessage[]): number {
  try {
    const totalChars = messages.reduce((sum, msg) => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content || '');
      return sum + content.length;
    }, 0);
    
    // 为消息结构和元数据添加开销
    const overhead = messages.length * 10; // 每条消息约 10 个 token 的结构开销
    
    return Math.ceil(totalChars / 4) + overhead;
  } catch (error) {
    console.warn('[PerformanceOptimizer] Token estimation failed, using fallback:', error);
    return messages.length * 100; // 超保守的回退：每条消息假设 100 个 token
  }
}
```

## 测试验证

### 测试结果
```bash
🧪 Testing tiktoken fix for custom models...
1. Creating model with custom name: ht::saas-deepseek-r1
✅ Model created successfully!
2. Testing token counting (this was failing before)...
✅ getNumTokens works! Count: 7
✅ getNumTokensFromMessages works! Result: { totalCount: 14, countPerMessage: [ 11 ] }
3. Testing with different custom model names...
✅ ht::saas-deepseek-v3: 2 tokens
✅ custom-claude-model: 2 tokens
✅ my-gemini-variant: 2 tokens
✅ unknown-model-123: 2 tokens

🎉 All tests passed! The tiktoken fix is working correctly.
```

### CLI 测试
```bash
echo "What is 2+2?" | node packages/lang/dist/cli.js --model ht::saas-deepseek-r1
# 输出: 4 (没有错误信息)
```

## 支持的模型映射

| 模型名称模式 | 映射到的 Tokenizer | 示例 |
|-------------|-------------------|------|
| 包含 `deepseek` 或 `r1` | `gpt-4` | `ht::saas-deepseek-r1` |
| 包含 `claude` | `gpt-4` | `custom-claude-model` |
| 包含 `gemini` | `gpt-3.5-turbo` | `my-gemini-variant` |
| 其他未知模型 | `gpt-3.5-turbo` | `unknown-model-123` |

## 回退机制

1. **主要方法**: 使用映射的已知模型名称进行 tiktoken 计算
2. **第一回退**: 基于字符数的估算（1 token ≈ 4 字符）
3. **最终回退**: 保守估算（每条消息 100 token）

## 影响范围

- ✅ 修复了自定义模型名称的 token 计算错误
- ✅ 保持了与标准 OpenAI 模型的兼容性
- ✅ 提供了健壮的回退机制
- ✅ 不影响现有功能
- ✅ 支持各种 OpenAI 兼容的 API 提供商

## 文件修改列表

1. `packages/lang/src/core/modelFactory.ts` - 主要修复
2. `packages/lang/src/core/performanceOptimizer.ts` - 改进 token 估算
3. `packages/lang/src/core/agent.ts` - 修复类型错误
4. `packages/lang/src/cli.ts` - 修复类型错误
5. `test-fix.js` - 验证测试

这个修复确保了 gemini-cli 可以与任何使用自定义模型名称的 OpenAI 兼容 API 正常工作，而不会遇到 tiktoken 的 "Unknown model" 错误。