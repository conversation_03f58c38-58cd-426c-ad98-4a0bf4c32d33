### 交互式流式输出修复说明（packages/lang）

**问题**
- 使用以下命令（CLI 包 + Lang<PERSON>hai<PERSON> 实现）可流式输出：
  - `GEMINI_CLI_USE_LANGCHAIN=true node packages/cli/dist/index.js -i`
- 但使用 Lang 包自身的 CLI 时无法流式输出：
  - `node packages/lang/dist/cli.js --interactive --debug`

**根因**
- `packages/lang` 使用 LangGraph 构建 Agent，但交互模式下的流媒体实现仍基于 `graph.stream(..., streamMode: "messages")`。该模式更偏向“按消息/段落”的更新，无法保证 token 级别/细粒度的流式体验。

**修复**
- 将 `packages/lang/src/core/stateGraphAgent.ts` 中的 `streamMessage` 从 `graph.stream(..., streamMode: "messages")` 切换为优先使用 `graph.streamEvents(..., version: 'v2')`：
  - 监听 `on_chat_model_stream`（以及兼容的 `on_llm_stream`）事件，逐 token/增量产出。
  - 对于不支持 `streamEvents` 的情况，保持向后兼容，回退到原有的 `streamMode: 'messages'` 流式方式。

**受影响文件**
- `packages/lang/src/core/stateGraphAgent.ts`

**验证步骤**
1. 构建：
   - `npm run -w packages/lang build`
2. 交互测试（需具备可用的模型环境，例如 OpenAI-Compatible）
   - `OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama node packages/lang/dist/cli.js --interactive --debug`
   - 输入问题后，观察是否出现逐字/逐片段的即时输出。

**注意**
- 本实现遵循 LangGraph JS 文档建议，`streamEvents` 为首选，保证更细粒度的流式体验；
- 保留向后兼容回退逻辑，避免在部分运行环境/版本下出现完全不流的退化。

**变更的用户可见效果**
- `node packages/lang/dist/cli.js --interactive` 现在与 `packages/cli` 的交互式体验一致，均可实时流式输出。


