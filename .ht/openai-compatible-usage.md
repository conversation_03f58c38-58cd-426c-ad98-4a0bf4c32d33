# OpenAI Compatible支持使用指南

## 概述

Lang包现在支持多种AI模型提供商，包括：
- **Gemini**: Google的Gemini模型（原有支持）
- **OpenAI**: OpenAI的GPT系列模型  
- **OpenAI Compatible**: 兼容OpenAI API的服务（如Ollama、LocalAI等）
- **Anthropic**: Anthropic的Claude系列模型

## 配置方式

### 1. OpenAI官方API

```typescript
import { createLangChainGeminiCLI } from '@ht/hai-code-cli';

// 设置环境变量
process.env.OPENAI_API_KEY = 'sk-your-openai-api-key';

const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1',
  model: 'gpt-4o-mini',           // 或 gpt-4o, gpt-4-turbo 等
  authType: 'USE_OPENAI_COMPATIBLE',
  targetDir: '/path/to/your/project',
  cwd: process.cwd(),
  debugMode: false,
});

// 使用CLI
const response = await cli.processMessage("帮我分析这个项目的结构");
console.log(response);
```

### 2. 自定义OpenAI兼容服务 (Ollama)

```typescript
// 设置Ollama endpoint
process.env.OPENAI_BASE_URL = 'http://localhost:11434/v1';
process.env.OPENAI_API_KEY = 'not-needed-for-ollama';

const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1', 
  model: 'llama2',               // Ollama模型名称
  authType: 'USE_OPENAI_COMPATIBLE',
  baseURL: 'http://localhost:11434/v1',  // 也可以直接传递
  targetDir: '/path/to/your/project',
  cwd: process.cwd(),
});
```

### 3. Anthropic Claude

```typescript
// 设置Anthropic API Key
process.env.ANTHROPIC_API_KEY = 'sk-ant-your-anthropic-key';

const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1',
  model: 'claude-3-5-sonnet-20241022',  // 或其他Claude模型
  authType: 'USE_ANTHROPIC', 
  targetDir: '/path/to/your/project',
  cwd: process.cwd(),
});
```

### 4. 混合使用（运行时切换）

```typescript
const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1',
  model: 'gemini-1.5-flash',
  authType: 'USE_GEMINI',
  targetDir: '/path/to/your/project',
  cwd: process.cwd(),
});

// 运行时切换到OpenAI
await cli.config.refreshAuth('USE_OPENAI_COMPATIBLE');
```

## 支持的模型

### OpenAI模型
- `gpt-4o`: 最新的GPT-4 Omni模型
- `gpt-4o-mini`: 经济型GPT-4 Omni
- `gpt-4-turbo`: GPT-4 Turbo
- `gpt-3.5-turbo`: GPT-3.5 Turbo

### Anthropic模型
- `claude-3-5-sonnet-20241022`: Claude 3.5 Sonnet (最新)
- `claude-3-haiku-20240307`: Claude 3 Haiku (快速)
- `claude-3-opus-20240229`: Claude 3 Opus (最强)

### OpenAI兼容服务
- **Ollama**: 本地运行的开源模型
  - `llama2`, `codellama`, `mistral` 等
- **LocalAI**: 自托管的OpenAI兼容API
- **其他**: 任何实现OpenAI API规范的服务

## 环境变量配置

```bash
# OpenAI官方API
export OPENAI_API_KEY="sk-your-openai-api-key"

# 自定义OpenAI兼容服务
export OPENAI_BASE_URL="http://localhost:11434/v1"  # Ollama示例
export OPENAI_API_KEY="not-needed"                  # 某些服务不需要

# Anthropic Claude
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"

# Gemini (原有)
export GEMINI_API_KEY="your-gemini-api-key"
export GOOGLE_CLOUD_PROJECT="your-gcp-project"
```

## 自动认证类型检测

Lang包会根据环境变量自动选择合适的认证类型：

```typescript
// 自动检测顺序:
// 1. ANTHROPIC_API_KEY -> USE_ANTHROPIC
// 2. OPENAI_API_KEY 或 OPENAI_BASE_URL -> USE_OPENAI_COMPATIBLE  
// 3. GOOGLE_GENAI_USE_VERTEXAI=true -> USE_VERTEX_AI
// 4. GEMINI_API_KEY -> USE_GEMINI
// 5. 默认 -> LOGIN_WITH_GOOGLE

const cli = await createLangChainGeminiCLI({
  sessionId: 'session-1',
  model: 'gpt-4o-mini',  // 将自动使用OpenAI
  targetDir: '/path/to/your/project',
  // authType 可省略，会自动检测
});
```

## 完整示例

```typescript
import { createLangChainGeminiCLI } from '@ht/hai-code-cli';

async function example() {
  // 设置OpenAI
  process.env.OPENAI_API_KEY = 'your-api-key';
  
  const cli = await createLangChainGeminiCLI({
    sessionId: 'demo-session',
    model: 'gpt-4o-mini',
    authType: 'USE_OPENAI_COMPATIBLE',
    targetDir: process.cwd(),
    cwd: process.cwd(),
    debugMode: true,
    coreTools: [              // 可选：指定要使用的工具
      'read_file',
      'list_directory', 
      'search_file_content',
      'replace',
    ],
  });

  console.log('🤖 AI助手已启动，使用OpenAI GPT-4o-mini模型');
  
  // 基础对话
  let response = await cli.processMessage(
    "你好！请帮我分析当前项目的结构。"
  );
  console.log('AI:', response);

  // 流式对话
  console.log('AI (streaming):');
  for await (const chunk of cli.streamMessage(
    "请读取README.md文件并总结主要内容。"
  )) {
    process.stdout.write(chunk);
  }
  
  console.log('\n✨ 对话完成');
}

example().catch(console.error);
```

## 错误处理

```typescript
import { createLangChainGeminiCLI, validateModelCompatibility } from '@ht/hai-code-cli';

async function robustExample() {
  // 验证模型兼容性
  const validation = validateModelCompatibility('gpt-4o', 'USE_OPENAI_COMPATIBLE');
  if (!validation.isValid) {
    console.warn(`模型不兼容，建议使用: ${validation.suggestion}`);
  }

  try {
    const cli = await createLangChainGeminiCLI({
      sessionId: 'session-1',
      model: 'gpt-4o',
      authType: 'USE_OPENAI_COMPATIBLE',
      targetDir: process.cwd(),
      cwd: process.cwd(),
    });

    const response = await cli.processMessage("Hello!");
    console.log(response);
    
  } catch (error) {
    if (error.message.includes('API key')) {
      console.error('❌ API密钥无效或缺失');
    } else if (error.message.includes('model')) {
      console.error('❌ 模型不支持或不可用');
    } else {
      console.error('❌ 未知错误:', error.message);
    }
  }
}
```

## 性能优化建议

1. **模型选择**:
   - 开发测试: `gpt-4o-mini`, `claude-3-haiku`
   - 生产环境: `gpt-4o`, `claude-3-5-sonnet`

2. **本地模型**:
   - 使用Ollama可降低成本和延迟
   - 适合隐私敏感的场景

3. **工具配置**:
   - 通过`coreTools`参数只启用需要的工具
   - 使用`excludeTools`排除不需要的工具

```typescript
const cli = await createLangChainGeminiCLI({
  // ...其他配置
  coreTools: ['read_file', 'replace'],  // 只启用必要工具
  excludeTools: ['web_search'],         // 排除特定工具
});
```

## 故障排除

### 常见问题

1. **API密钥错误**:
   ```bash
   export OPENAI_API_KEY="your-correct-key"
   ```

2. **模型不可用**:
   - 检查模型名称拼写
   - 确认API权限

3. **连接问题**:
   - 检查网络连接
   - 验证baseURL设置

4. **Ollama配置**:
   ```bash
   # 启动Ollama服务
   ollama serve
   
   # 拉取模型
   ollama pull llama2
   ```

### 调试模式

```typescript
const cli = await createLangChainGeminiCLI({
  // ...
  debugMode: true,  // 启用详细日志
});
```

这样就可以在控制台看到详细的API调用和错误信息。