# 工具参数传递修复说明

## 问题分析

从测试日志可以看出工具调用的参数传递问题：

### 🔍 **错误日志分析**

```
[LangChainAgent] Executing tool calls in stream: [ 'list_directory' ]
🔧 正在使用工具: list_directory

[CoreToolWrapper] Input is not JSON, treating as string: ./
[CoreToolWrapper] Error executing list_directory: Error: Tool validation failed: params must have required property 'path'
```

**关键发现**:
1. ✅ **工具调用检测成功**: LLM生成了正确的工具调用
2. ✅ **工具执行启动**: Agent开始执行工具
3. ❌ **参数格式错误**: 参数传递格式不匹配

### 🐛 **根本原因**

**参数结构不匹配**:
- **LangChain传递**: `{"input": "./"}`（通用input字段）
- **Core工具期望**: `{"path": "./"}`（具体参数名）

**调用流程问题**:
1. LLM生成: `{"function":{"arguments":"{\"input\":\"./\"}","name":"list_directory"}}`
2. Lang<PERSON>hai<PERSON>解析: `toolCall.args = {"input": "./"}`
3. Agent调用: `tool.invoke({"input": "./"})`
4. Core工具验证: 期望`{"path": "./"}`，但收到`{"input": "./"}`
5. 验证失败: `params must have required property 'path'`

## 解决方案

### 🔧 **修复策略**

#### 1. **重写invoke方法处理对象参数**

```typescript
// 修复前 - 只处理字符串
async _call(input: string | undefined): Promise<string> {
  // 只能处理字符串输入
}

// 修复后 - 直接处理对象参数
async invoke(input: Record<string, unknown> | string): Promise<string> {
  let args: Record<string, unknown>;
  
  if (typeof input === 'object' && input !== null) {
    // 直接使用对象参数
    args = input;
  } else if (typeof input === 'string') {
    // 处理字符串输入
    args = JSON.parse(input);
  }
  // ... 执行工具
}
```

#### 2. **为每个工具定义正确的Schema**

```typescript
private generateSchemaFromCoreTool(coreTool: CoreTool): any {
  const schemaFields: Record<string, z.ZodTypeAny> = {};
  
  switch (coreTool.name) {
    case 'list_directory':
      schemaFields.path = z.string().describe('The absolute path to the directory to list');
      schemaFields.ignore = z.array(z.string()).optional().describe('List of glob patterns to ignore');
      break;
      
    case 'glob':
      schemaFields.pattern = z.string().describe('The glob pattern to match against');
      schemaFields.path = z.string().optional().describe('The directory to search in');
      break;
      
    case 'run_shell_command':
      schemaFields.command = z.string().describe('Exact bash command to execute');
      schemaFields.description = z.string().optional().describe('Brief description of the command');
      break;
      
    // ... 其他工具
  }
  
  return z.object(schemaFields);
}
```

#### 3. **修复参数传递流程**

**新的流程**:
1. **Schema定义**: 每个工具有正确的参数schema
2. **LLM生成**: 基于schema生成正确的工具调用
3. **参数传递**: `tool.invoke(toolCall.args)`直接传递对象
4. **参数处理**: `invoke`方法直接处理对象参数
5. **工具执行**: Core工具收到正确格式的参数

### 📊 **修复验证**

通过检查编译后的代码，确认修复已正确应用：

1. **invoke方法**: ✅ 新增了处理对象参数的invoke方法
2. **Schema定义**: ✅ 为每个工具定义了正确的参数schema
3. **参数处理**: ✅ 正确处理对象和字符串输入
4. **错误处理**: ✅ 保持了完整的错误处理逻辑

## 预期效果

修复后的工具调用流程：

### **成功的工具调用日志**

```
[LangChainAgent] Executing tool calls in stream: [ 'list_directory' ]
🔧 正在使用工具: list_directory

[CoreToolWrapper] Received object input for list_directory: {"path": "./"}
[工具执行成功，返回目录列表]
[LLM基于工具结果生成回答]
```

### **参数映射示例**

| 工具名称 | LLM生成参数 | Core工具期望 | 状态 |
|---------|------------|-------------|------|
| `list_directory` | `{"path": "./"}` | `{"path": "./"}` | ✅ 匹配 |
| `glob` | `{"pattern": "**/*"}` | `{"pattern": "**/*"}` | ✅ 匹配 |
| `run_shell_command` | `{"command": "pwd"}` | `{"command": "pwd"}` | ✅ 匹配 |

## 技术说明

### **LangChain工具调用机制**

1. **Schema绑定**: LangChain使用工具的schema告知LLM参数结构
2. **参数生成**: LLM基于schema生成符合要求的参数
3. **参数传递**: LangChain调用`tool.invoke(args)`传递参数对象
4. **工具执行**: 工具的invoke方法处理参数并执行

### **Core工具参数要求**

每个core工具都有特定的参数要求：

- **list_directory**: `{path: string, ignore?: string[]}`
- **glob**: `{pattern: string, path?: string, case_sensitive?: boolean}`
- **run_shell_command**: `{command: string, description?: string, directory?: string}`
- **read_file**: `{path: string}`
- **search_file_content**: `{query: string, path?: string}`
- **replace**: `{path: string, old_str: string, new_str: string}`
- **write_file**: `{path: string, content: string}`

### **兼容性考虑**

修复后的代码保持了向后兼容性：

- **invoke方法**: 处理对象参数（主要用途）
- **_call方法**: 处理字符串参数（兼容性）
- **错误处理**: 统一的错误处理和日志记录

这个修复解决了工具参数传递的核心问题，确保LangChain生成的工具调用参数能够正确传递给core工具执行。

## 测试建议

使用相同的测试命令：
```bash
node ./packages/lang/dist/cli.js --interactive --debug
```

预期看到的成功日志：
```
[LangChainAgent] Executing tool calls in stream: [ 'list_directory' ]
🔧 正在使用工具: list_directory

[CoreToolWrapper] Received object input for list_directory: {"path": "/current/directory"}
[工具执行成功，返回目录内容]
[LLM基于工具结果生成项目介绍]
```

修复后，工具调用应该能够成功执行，不再出现参数验证错误。
