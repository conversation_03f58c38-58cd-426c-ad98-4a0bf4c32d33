$ node packages/lang/dist/cli.js --interactive --debug

Debug mode enabled
Environment variables:
  OPENAI_API_KEY: √
  OPENAI_BASE_URL: http://168.63.85.222/web/unauth/LLM_api_proxy/v1
  ANTHROPIC_API_KEY: ×
  GEMINI_API_KEY: ×
  GOOGLE_CLOUD_PROJECT: flash-zenith-464113-v3
Auth type: USE_OPENAI_COMPATIBLE
Model: ht::saas-deepseek-v3

Flushing log events to Clearcut.
Discovered and registered 9 core tools
[LangChainToolRegistry] Starting MCP tool discovery...
n[LangChainMCPClient] Discovered MCP tool: convert_time
[LangChainMCPClient] Discovered MCP tool: get_current_time
[LangChainMCPClient] Successfully discovered 2 MCP tools
[LangChainToolRegistry] Registered MCP tool: convert_time
[LangChainToolRegistry] Registered MCP tool: get_current_time
[LangChainToolRegistry] Successfully registered 2 MCP tools
✅ LangChain components initialized successfully
[SessionManager] Skipping empty session: 18f3b82b-c212-40ca-9fd3-c861ee7ee7d9
[SessionManager] Skipping empty session: 35944c47-85ec-4990-8247-6ed2e2d8b2d9
[SessionManager] Skipping empty session: 6ece808a-acc1-4b87-a005-87e8259eb80e
🤖 hai-code - Interactive mode
Type "exit" or "quit" to exit, Ctrl+C to quit immediately

[CLI] Created interactive session: de5dac0c-4f9b-443d-9da8-d41354d5fa1e
> n[SessionManager] Skipping empty session: cb2b1a9d-9d9a-444f-b4fe-d4d102de7d5a
[SessionManager] Skipping empty session: d80a79cf-a6da-4cbf-922c-a303cd95c7fb
[SessionManager] Skipping empty session: d9ed4eb5-4cdb-4f23-9321-f4bf0685489a
[SessionManager] Skipping empty session: da11cbfb-d47a-4ab2-85fb-985d2d729203
[SessionManager] Skipping empty session: ddbe2abb-2249-457d-af77-a40f32fe523d
[SessionManager] Skipping empty session: e13cdca3-6168-4971-9a4f-89792845a25e
[SessionManager] Loaded 1 sessions from disk
> 你好

[CLI] Received input: "你好"
[SessionManager] Created session: de5dac0c-4f9b-443d-9da8-d41354d5fa1e (logger will initialize on first use)
[CLI] Mapped interactive session de5dac0c-4f9b-443d-9da8-d41354d5fa1e to actual session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
[StateGraphAgent] Intelligent streaming for: 你好
[OptimizedStream] Tools bound to model for streaming
[SessionManager] Saved session de5dac0c-4f9b-443d-9da8-d41354d5fa1e to disk
你好！有什么可以帮您的吗？[SessionManager] Updated session de5dac0c-4f9b-443d-9da8-d41354d5fa1e with 2 messages
[streamMessage] Added 2 messages to session de5dac0c-4f9b-443d-9da8-d41354d5fa1e (fallback)

[CLI] Stream completed: 8 chunks, 13 chars


[CLI] Prompting for next input
> [HaicodeLogger] Initialized logger for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
[SessionManager] Logger initialized for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
Saved session metadata to /Users/<USER>/.haicode/tmp/__Users__xy__projs__github__gemini-cli/sessions/de5dac0c-4f9b-443d-9da8-d41354d5fa1e/session.json
[HaicodeLogger] Added log entry to memory for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
[HaicodeLogger] Added log entry to memory for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
[SessionManager] Saved session de5dac0c-4f9b-443d-9da8-d41354d5fa1e to disk
介绍一下当前仓库

[CLI] Received input: "介绍一下当前仓库"
[StateGraphAgent] Intelligent streaming for: 介绍一下当前仓库
[OptimizedStream] Tools bound to model for streaming


🔧 调用工具: list_directory

🔧 执行工具: list_directory
📥 输入: {
  "path": "/Users/<USER>/projs/github/gemini-cli"
}[CoreToolWrapper] Validating tool params: {"path":"/Users/<USER>/projs/github/gemini-cli"}, tool name: list_directory
[ToolResultProcessor] Tool list_directory result: Directory listing for /Users/<USER>/projs/github/gemini-cli:
[DIR] .augment
[DIR] .codebuddy
[DIR] .cursor
[DIR] .gcp
[DIR] .gemini
[DIR] .github
[DIR] .roo-code
[DIR] .trae
[DIR] .vscode
[DIR] docs
[DIR] integration-tests
[DIR] packages
[DIR] scripts
.editorconfig
.gitattributes
.gitignore
.npmrc
.nvmrc
.prettierrc.json
CONTRIBUTING-zh.md
CONTRIBUTING.md
Dockerfile
esbuild.config.js
eslint.config.js
GEMINI.md
LICENSE
Makefile
package-lock.json
package.json
README.md
ROADMAP.md
tsconfig.json

(5 git...(truncated)

📤 输出: Directory listing for /Users/<USER>/projs/github/gemini-cli:
[DIR] .augment
[DIR] .codebuddy
[DIR] .cursor
[DIR] .gcp
[DIR] .gemini
[DIR] .github
[DIR] .roo-code
[DIR] .trae
[DIR] .vscode
[DIR] docs
[DIR] integration-tests
[DIR] packages
[DIR] scripts
.editorconfig
.gitattributes
.gitignore
.npmrc
.nvmrc
.prettierrc.json
CONTRIBUTING-zh.md
CONTRIBUTING.md
Dockerfile
esbuild.config.js
eslint.config.js
GEMINI.md
LICENSE
Makefile
package-lock.json
package.json
README.md
ROADMAP.md
tsconfig.json

(5 git...(省略)

💭 分析结果...[SessionManager] Updated session de5dac0c-4f9b-443d-9da8-d41354d5fa1e with 2 messages
[streamMessage] Added 2 messages to session de5dac0c-4f9b-443d-9da8-d41354d5fa1e (fallback)
[HaicodeLogger] Added log entry to memory for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e
[HaicodeLogger] Added log entry to memory for session de5dac0c-4f9b-443d-9da8-d41354d5fa1e

[CLI] Stream completed: 5 chunks, 634 chars


[CLI] Prompting for next input
> [SessionManager] Saved session de5dac0c-4f9b-443d-9da8-d41354d5fa1e to disk
