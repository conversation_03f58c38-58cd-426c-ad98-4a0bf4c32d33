$ hai-code -m ht::saas-deepseek-r1 --interactive --debug

Debug mode enabled
Environment variables:
  OPENAI_API_KEY: √
  OPENAI_BASE_URL: http://168.63.85.222/web/unauth/LLM_api_proxy/v1
  ANTHROPIC_API_KEY: ×
  GEMINI_API_KEY: ×
  GOOGLE_CLOUD_PROJECT: flash-zenith-464113-v3
Auth type: USE_OPENAI_COMPATIBLE
Model: ht::saas-deepseek-r1

Flushing log events to Clearcut.
Discovered and registered 11 core tools
[LangChainToolRegistry] Starting MCP tool discovery...
[LangChainMCPClient] Discovered MCP tool: convert_time
[LangChainMCPClient] Discovered MCP tool: get_current_time
[LangChainMCPClient] Successfully discovered 2 MCP tools
[LangChainToolRegistry] Registered MCP tool: convert_time
[LangChainToolRegistry] Registered MCP tool: get_current_time
[LangChainToolRegistry] Successfully registered 2 MCP tools
✅ LangChain components initialized successfully
[Hai<PERSON>Logger] Initialized logger for session 17bca415-f5d2-4de8-9294-dcd06cc2c12b
[HaicodeLogger] Initialized logger for session 266eb233-1a42-4925-8fc5-ea90b80c79af
[HaicodeLogger] Initialized logger for session 36f51cf8-e839-4573-b592-ab5f745816bf
[HaicodeLogger] Initialized logger for session 3b40878f-bfc6-4c14-ac7d-d79c58e743c4
[HaicodeLogger] Initialized logger for session 3d6a082d-547b-4520-a641-ae30121c51b1
[HaicodeLogger] Initialized logger for session 498ffb99-15d0-41ba-8223-842321604684
[HaicodeLogger] Initialized logger for session 52d31e07-a54c-4956-8db0-abb661f6089c
[HaicodeLogger] Initialized logger for session 5fd8137e-6d42-42ad-bc6e-d1c8c7a14ea4
[HaicodeLogger] Initialized logger for session 6c337a18-462d-4c6e-8257-e08cf4646f4d
[HaicodeLogger] Initialized logger for session 7ee63379-409a-4f1b-be89-cfb5a125c485
[HaicodeLogger] Initialized logger for session 8a04d3c4-c4de-4e56-9a1a-2118acf5d609
[HaicodeLogger] Initialized logger for session b54f15b2-e47f-48bc-8f2f-670a7e2b4a50
[HaicodeLogger] Initialized logger for session b686b58e-b949-438a-a1e9-1d755257a555
[HaicodeLogger] Initialized logger for session c11e6d7b-e4ce-43a1-8df5-4405714d62ba
[HaicodeLogger] Initialized logger for session f451b509-ee94-46df-8801-8bbc1189dad6
[HaicodeLogger] Initialized logger for session f6fbb55b-ea15-41d0-bb95-24399b0d4eb5
[SessionManager] Loaded 16 sessions from disk
🤖 hai-code - Interactive mode
Type "exit" or "quit" to exit, Ctrl+C to quit immediately

[CLI] Created interactive session: e1e48638-9cb7-476f-9eaf-a097aaa2609f
> 法国首都在哪里

[CLI] Received input: "法国首都在哪里"

[CLI] Starting to stream message for session: e1e48638-9cb7-476f-9eaf-a097aaa2609f
[SessionManager] Created session: e1e48638-9cb7-476f-9eaf-a097aaa2609f
[CLI] Mapped interactive session e1e48638-9cb7-476f-9eaf-a097aaa2609f to actual session e1e48638-9cb7-476f-9eaf-a097aaa2609f
Haicode logger not initialized. Cannot log message.
[SessionManager] Updated session e1e48638-9cb7-476f-9eaf-a097aaa2609f with 1 messages
[StateGraphAgent] Streaming message: 法国首都在哪里
[StateGraphAgent] Starting stream with config: {
  "recursionLimit": 25,
  "configurable": {
    "thread_id": "e1e48638-9cb7-476f-9eaf-a097aaa2609f"
  }
}
[HaicodeLogger] Initialized logger for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[SessionManager] Logger initialized for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
Saved session metadata to /Users/<USER>/.haicode/tmp/__Users__xy__projs__gdp__gdp-monitor-fe/sessions/e1e48638-9cb7-476f-9eaf-a097aaa2609f/session.json
[SessionManager] Saved 17 sessions to disk
[SessionManager] Saved 17 sessions to disk
[SessionManager] Saved 17 sessions to disk
[StateGraphAgent] Calling model with 1 messages
[StateGraphAgent] Tools successfully bound to model
[StateGraphAgent] Invoking model with 2 messages
[StateGraphAgent] Model response received: AIMessageChunk with content
[StateGraphAgent] Tool calls detected, continuing to tools
[工具调用] 🔧 StateGraph执行工具: google_web_search
[工具调用] 📥 工具 google_web_search 输入参数: {
  "query": "法国首都在哪里"
}
[CoreToolWrapper] Validating tool params: {"query":"法国首都在哪里"}, tool name: google_web_search
Error during web search for query "法国首都在哪里": Cannot read properties of undefined (reading 'generateContent') TypeError: Cannot read properties of undefined (reading 'generateContent')
    at WebSearchTool.execute (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@google/gemini-cli-core/dist/src/tools/web-search.js:58:49)
    at CoreToolWrapper.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/dist/tools/toolRegistry.js:54:48)
    at file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/prebuilt/tool_node.js:162:43
    at Array.map (<anonymous>)
    at ToolNode.run (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/prebuilt/tool_node.js:156:63)
    at ToolNode.func (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/prebuilt/tool_node.js:126:59)
    at file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:113
    at AsyncLocalStorage.run (node:async_hooks:346:14)
    at AsyncLocalStorageProvider.runWithConfig (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/singletons/async_local_storage/index.js:56:24)
    at ToolNode.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:68)
[工具调用] ✅ 工具执行完成
[工具调用] 📤 输出结果: Error: Error during web search for query "法国首都在哪里": Cannot read properties of undefined (reading 'generateContent')
[StateGraphAgent] Calling model with 3 messages
[StateGraphAgent] Tools successfully bound to model
[StateGraphAgent] Invoking model with 4 messages
[StateGraphAgent] Error calling model: TypeError: Cannot read properties of undefined (reading 'message')
    at ChatOpenAI.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/language_models/chat_models.js:91:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async StateGraphAgent.callModel (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/dist/core/stateGraphAgent.js:150:30)
    at async RunnableCallable.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:27)
    at async RunnableSequence.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/runnables/base.js:1297:33)
    at async _runWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/retry.js:72:22)
    at async PregelRunner._executeTasksWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:206:33)
    at async PregelRunner.tick (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:68:55)
    at async CompiledStateGraph._runLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1478:17)
    at async createAndRunLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1373:17)
[StateGraphAgent] Error streaming message: TypeError: Cannot read properties of undefined (reading 'message')
    at ChatOpenAI.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/language_models/chat_models.js:91:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async StateGraphAgent.callModel (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/dist/core/stateGraphAgent.js:150:30)
    at async RunnableCallable.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:27)
    at async RunnableSequence.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/runnables/base.js:1297:33)
    at async _runWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/retry.js:72:22)
    at async PregelRunner._executeTasksWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:206:33)
    at async PregelRunner.tick (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:68:55)
    at async CompiledStateGraph._runLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1478:17)
    at async createAndRunLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1373:17) {
  pregelTaskId: '3eea1757-e63f-5fd5-8926-c0852ff2b2ca'
}
Error: Cannot read properties of undefined (reading 'message')[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[SessionManager] Updated session e1e48638-9cb7-476f-9eaf-a097aaa2609f with 2 messages

[CLI] Stream completed: 1 chunks, 62 chars


[CLI] Prompting for next input
> [SessionManager] Saved 17 sessions to disk
[SessionManager] Saved 17 sessions to disk
>  访问 http://www.baidu/com 并截图保存为一张图

[CLI] Received input: "访问 http://www.baidu/com 并截图保存为一张图"

[CLI] Starting to stream message for session: e1e48638-9cb7-476f-9eaf-a097aaa2609f
[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[SessionManager] Updated session e1e48638-9cb7-476f-9eaf-a097aaa2609f with 1 messages
[StateGraphAgent] Streaming message: 访问 http://www.baidu/com 并截图保存为一张图
[StateGraphAgent] Starting stream with config: {
  "recursionLimit": 25,
  "configurable": {
    "thread_id": "e1e48638-9cb7-476f-9eaf-a097aaa2609f"
  }
}
[SessionManager] Saved 17 sessions to disk
[SessionManager] Saved 17 sessions to disk
[StateGraphAgent] Calling model with 7 messages
[StateGraphAgent] Tools successfully bound to model
[StateGraphAgent] Invoking model with 8 messages
[StateGraphAgent] Error calling model: TypeError: Cannot read properties of undefined (reading 'message')
    at ChatOpenAI.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/language_models/chat_models.js:91:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async StateGraphAgent.callModel (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/dist/core/stateGraphAgent.js:150:30)
    at async RunnableCallable.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:27)
    at async RunnableSequence.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/runnables/base.js:1297:33)
    at async _runWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/retry.js:72:22)
    at async PregelRunner._executeTasksWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:206:33)
    at async PregelRunner.tick (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:68:55)
    at async CompiledStateGraph._runLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1478:17)
    at async createAndRunLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1373:17)
[StateGraphAgent] Error streaming message: TypeError: Cannot read properties of undefined (reading 'message')
    at ChatOpenAI.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/language_models/chat_models.js:91:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async StateGraphAgent.callModel (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/dist/core/stateGraphAgent.js:150:30)
    at async RunnableCallable.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/utils.js:79:27)
    at async RunnableSequence.invoke (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/core/dist/runnables/base.js:1297:33)
    at async _runWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/retry.js:72:22)
    at async PregelRunner._executeTasksWithRetry (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:206:33)
    at async PregelRunner.tick (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/runner.js:68:55)
    at async CompiledStateGraph._runLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1478:17)
    at async createAndRunLoop (file:///Users/<USER>/.nvm/versions/node/v20.19.0/lib/node_modules/@ht/hai-code-cli/node_modules/@langchain/langgraph/dist/pregel/index.js:1373:17) {
  pregelTaskId: '92632f4d-3582-57ea-94f5-f5a3577d57a6'
}
Error: Cannot read properties of undefined (reading 'message')[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[HaicodeLogger] Added log entry to memory for session e1e48638-9cb7-476f-9eaf-a097aaa2609f
[SessionManager] Updated session e1e48638-9cb7-476f-9eaf-a097aaa2609f with 3 messages

[CLI] Stream completed: 1 chunks, 62 chars


[CLI] Prompting for next input
> [SessionManager] Saved 17 sessions to disk
[SessionManager] Saved 17 sessions to disk
