# CLI 接入 packages/lang 适配说明

## 目标
- 在不入侵现有 `packages/cli` 主要流程的前提下，支持通过环境变量切换为 `packages/lang` 的 LangChain/LangGraph 实现。

## 开关
- 通过环境变量 `GEMINI_CLI_USE_LANGCHAIN=true` 启用。

## 交互式模式
- 文件：`packages/cli/src/integration/langInteractiveAdapter.ts`
- 逻辑：在 `gemini.tsx` 初始化后调用 `adaptConfigForLangInteractive(config, { checkpointing })`，动态导入 `@ht/hai-code-cli`，创建代理并以 `GeminiClient` shim 形式接入 UI。

## 非交互式模式
- 新增文件：`packages/cli/src/integration/langNonInteractiveRunner.ts`
- 逻辑：当开关打开时，`gemini.tsx` 在非交互分支调用 `runNonInteractiveLang(config, input, prompt_id)`，使用 `@ht/hai-code-cli` 的 `streamMessage` 直接流式输出。

## 变更点
- `packages/cli/src/gemini.tsx`
  - 引入 `adaptConfigForLangInteractive` 与 `runNonInteractiveLang`，并在对应分支调用。
- 新增：`packages/cli/src/integration/langNonInteractiveRunner.ts`

## 兼容性
- 当未设置开关或导入失败时，回退到原有 Core 实现，交互式与非交互式均保持原行为。

## 注意
- 适配层尽量只做最薄的一层：动态导入、参数映射、流式输出；不修改原有工具/鉴权/主题等核心逻辑。

## 认证类型扩展（ExtendedAuthType）
- 为与 LangChain.js 最佳实践对齐，CLI 兼容两类新增认证：
  - `USE_OPENAI_COMPATIBLE`：需要 `OPENAI_API_KEY` 与 `OPENAI_BASE_URL`
  - `USE_ANTHROPIC`：需要 `ANTHROPIC_API_KEY`
- 改动位置：
  - `packages/cli/src/config/auth.ts`：扩展 `validateAuthMethod` 支持上述两种类型，仅在 `GEMINI_CLI_USE_LANGCHAIN=true` 时生效。
  - `packages/cli/src/validateNonInterActiveAuth.ts`：解析有效认证类型时，若开关启用，会优先选择 Anthropic / OpenAI 兼容，再回退 Vertex/Gemini。


