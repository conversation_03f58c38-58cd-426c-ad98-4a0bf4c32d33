# 流式输出（Streaming）相关历史提交与影响梳理

- 强相关目录与文件：
  - `packages/lang/src/cli.ts`
  - `packages/lang/src/index.ts`
  - `packages/lang/src/core/stateGraphAgent.ts`

## 关键提交一览（按时间）

- 440b39b4 (2025-08-01) feat: 交互模式没有问题，是输入方式的问题，已补充使用步骤
  - 变化点（`packages/lang/src/cli.ts`）：
    - 交互模式使用 for-await 逐 chunk 写入 `process.stdout.write(chunk)`，并增加调试日志（`chunkCount`、累计长度等）。
  - 影响：
    - 明确了交互模式采用流式打印的路径，验证了 CLI 端逐块输出逻辑可行。

- 2940031b (2025-08-01) fix: 思考过程打印
  - 变化点（`packages/lang/src/cli.ts`）：
    - 非交互路径从一次性 `processMessage` 切换为 `streamMessage` + for-await 逐 chunk 输出。
  - 影响：
    - 非交互模式也改为流式输出。

- 1a76e1b6 (2025-08-08) feat: 接入mongodb + langfuse(30%)
  - 变化点（`packages/lang/src/core/stateGraphAgent.ts`）：
    - 为 `graph.invoke`/`graph.stream` 增加 callbacks 透传；`graph.stream(initialState, { ...config, streamMode: "messages" })`。
  - 影响：
    - 保持 messages 级别流（非 token 事件）。

- 20647b25 (2025-08-08) feat: 接入langfuse + 移除agent中streamEvents
  - 变化点（`packages/lang/src/core/stateGraphAgent.ts`）：
    - 移除了 `async *streamEvents(...)`，此前该方法使用 `graph.streamEvents(..., version: "v2")`，在 `on_chat_model_stream` 事件中对 `AIMessageChunk` 的 `chunk.content` 逐 token `yield`。
  - 影响（高风险）：
    - 移除 token 级别事件流后，当前仅保留 `streamMode: "messages"` 的 `graph.stream(...)` 路径；在多数实现中，这只会按“消息”粒度产出，无法保证逐 token 增量（表现为只在完成时一次性输出）。这与当前反馈的“`node packages/lang/dist/cli.js --interactive --debug` 不再流式输出”一致。

- f508a6df (2025-08-08) fix: cli兼容 20%（不调用工具时可交互）
  - 变化点（`packages/lang/src/core/stateGraphAgent.ts`）：
    - 在 `streamMessage` 内加入 `hasYieldedContent` 追踪与空内容报错；逐步遍历 `this.messageStream(message)` 并 `yield`。
  - 影响：
    - 逻辑上仍支持流式，但遇到没有内容时可能直接抛错中断。

- 4e701054 (2025-08-08) feat: 恢复agent中message处理逻辑 - 简化
  - 变化点（`packages/lang/src/core/stateGraphAgent.ts`）：
    - 移除 `hasYieldedContent` 逻辑，改回 `yield* this.messageStream(message)` 的简化实现。
  - 影响：
    - 精简了流转发实现，但仍依赖上层是 message 级别还是 token 级别事件。

- 4e0aeb47 (2025-08-08) feat: rename agent
  - 变化点（`packages/lang/src/cli.ts`）：
    - `createLangChainCLI` -> `createHaicodeAgent`，调用点相应更名。
  - 影响：
    - 不直接影响流式，但确认 CLI 端仍以 `for await (const chunk of agent.streamMessage(...))` 输出。

## 现象与根因定位

- 现象：`node packages/lang/dist/cli.js --interactive --debug` 只在结束时输出，不再逐 token 流式。
- 高可能根因：`20647b25` 移除了 `streamEvents`，当前仅使用 `graph.stream(..., streamMode: "messages")`。在 LangGraph 的常见实现中：
  - `streamMode: "messages"`：按节点/消息级别产出，通常不是逐 token；
  - `streamEvents` + 监听 `on_chat_model_stream`：可拿到 `AIMessageChunk`，逐 token 产出。

## 建议修复方向（两选一）

- 建议A（推荐）：恢复 `streamEvents` 路径并在 `StateGraphAgent.streamMessage` 内优先使用 `streamEvents` 以实现逐 token：
  - 恢复 `async *streamEvents(...)`，监听 `on_chat_model_stream` 事件，对 `AIMessageChunk` 的 `chunk.content` 逐 token `yield`；
  - `streamMessage` 内优先走 `streamEvents`，仅在不支持时回退 `graph.stream(..., streamMode: "messages")`。

- 建议B：若使用的 LangGraph 版本已支持 token 级别的 `streamMode`（例如某些实现支持更细粒度模式），将 `streamMode` 改为相应 token 模式；否则继续采用 A。

## 排查与验证点

- 验证 `packages/lang/src/cli.ts`：
  - 交互与非交互均为 for-await 逐 chunk `process.stdout.write`（当前已满足）。
- 验证 `packages/lang/src/index.ts#createHaicodeAgent.streamMessage`：
  - 逐 chunk `yield`，不做缓冲合并（当前已逐 chunk `yield`）。
- 核心在于 `StateGraphAgent` 是否提供逐 token 的 `yield`：
  - 恢复或改用 token 级流后，CLI 将按预期逐 token 打印。

## 快速回滚/对齐参考

- 若需验证回退效果，可临时基于 `20647b25` 之前的实现恢复 `streamEvents`（仅文件：`packages/lang/src/core/stateGraphAgent.ts`），并确保 `graph.streamEvents(..., { version: "v2" })` 与 `AIMessageChunk` 判断分支一致。
