## packages/lang 接入 packages/cli 可行性评估与改造说明

### 结论
- 在非交互模式（stdin 输入、`-p/--prompt`）下可通过环境变量 `GEMINI_CLI_USE_LANGCHAIN=true` 无侵入地切换到 `packages/lang` 的流式执行，已完成接入。
- 交互模式（Ink UI）依赖 `@google/gemini-cli-core` 的 `Config/GeminiClient`、hooks 及工具生命周期，直接替换为 `packages/lang` 成本较高，短期不建议无缝切换，需进一步适配 UI 层。

### 关键差异与问题点
- **对象模型差异**：
  - CLI UI 期望 `Config` 提供 `getGeminiClient()/getToolRegistry()`、`getModel()` 等方法；`packages/lang` 暴露 `createHaicodeAgent()` 返回 `{ config, sessionManager, agent, processMessage, streamMessage }`，不是 `Config` 接口。
  - 非交互流程 `runNonInteractive` 基于 `GeminiClient.sendMessageStream()` 和 `ToolRegistry`；`packages/lang` 以 `agent.streamMessage()` 产出字符串流。
- **工具集成差异**：
  - `packages/lang` 通过 `LangChainToolRegistry/CoreToolWrapper` 包装 core 工具，调用与 core 的 `ToolRegistry` 接口不同，但功能等价。
- **MCP/扩展**：
  - CLI 的扩展与 MCP 配置由 core `Config` 管理；`packages/lang` 具备 MCP 适配器，但未与 CLI 的扩展发现流程做绑定。

### 已完成的最小改造
- `packages/cli/src/gemini.tsx`：在非交互模式检测到 `GEMINI_CLI_USE_LANGCHAIN=true` 时，动态加载 `@ht/hai-code-cli`，调用 `createHaicodeAgent` 并使用 `streamMessage()` 输出；失败则回退至原 `runNonInteractive`。
- `packages/cli/package.json`：加入工作区依赖 `@ht/hai-code-cli` 指向 `../lang`。

### 后续建议的阶段性改造（可选）
1. UI 兼容层：在 `packages/lang` 提供一个 `CoreCompatibleConfigAdapter`，实现 `Config` 的只读子集方法（`getModel/getDebugMode/getGeminiClient` 以代理形式），使 Ink UI 在交互模式可启用 LangChain 模式。
2. MCP/扩展桥接：在 `packages/lang` 的 `LangChainConfig` 中消费 CLI 的扩展与 MCP 配置，统一工具发现入口。
3. 统一遥测与检查点：将 `langfuse` 与 `checkpointing` 标志与 CLI 设置联动（已在非交互模式初步打通）。

### 使用方式
- 构建 `packages/lang`：
  - `npm run build --workspace=packages/lang`
- 运行（非交互）启用 LangChain：
  - `GEMINI_CLI_USE_LANGCHAIN=true node packages/cli/dist/index.js -p "your prompt"`

### 自定义 LLM（custom-llm-api）支持
- 已在 CLI 做最小改动以允许在 LangChain 模式下启用自定义/兼容型提供商：
  - `packages/cli/src/config/auth.ts`: 允许 `custom-llm-api`/`USE_OPENAI_COMPATIBLE` 通过校验。
  - `packages/cli/src/validateNonInterActiveAuth.ts`: 在 `GEMINI_CLI_USE_LANGCHAIN=true` 时，自动检测 `OPENAI_API_KEY`/`OPENAI_BASE_URL` 并采用 OpenAI 兼容认证。
- 使用：
  - 环境变量：
    - `GEMINI_CLI_USE_LANGCHAIN=true`
    - `OPENAI_BASE_URL=https://your-endpoint`
    - `OPENAI_API_KEY=sk-...`
  - 交互：`node packages/cli/dist/index.js --prompt-interactive "hi"`
  - 非交互：`node packages/cli/dist/index.js -p "explain code"`
- 说明：
  - 交互流走 `packages/lang` 的流式文本路径；如遇异常自动回退到 core（非交互）。
  - 工具事件仍未在交互适配器中发射，后续可按需完善。

### 风险与回退
- 若 `@ht/hai-code-cli` 加载失败或执行异常，将自动回退到 core 非交互流程；开启 `--debug` 可看到回退原因。


