### MongoDB 本地准备与 createLangChainCLI 传参与验证

本文档说明如何在本地安装与准备 MongoDB，并给出在 `createLangChainCLI`（原 `createLangChainGeminiCLI`）中启用 MongoDB Checkpointer 与可选 Langfuse 追踪的参数示例，以及基本的验证与排错建议。

---

### 1) 环境准备（macOS）

- 安装与启动（Homebrew）
```bash
brew tap mongodb/brew
brew install mongodb-community@7.0
brew services start mongodb-community@7.0
# 查看服务状态
brew services list
```

- 默认参数
  - 端口：`27017`
  - 数据目录（Apple Silicon）：`/opt/homebrew/var/mongodb`（Intel: `/usr/local/var/mongodb`）

- 验证服务可用
```bash
mongosh
use admin
db.runCommand({ ping: 1 })
```

- （可选）创建数据库与账号（若本地默认无鉴权可跳过）
```bash
mongosh
use gemini_cli
db.createUser({
  user: 'gemini',
  pwd: 'your_password',
  roles: [{ role: 'readWrite', db: 'gemini_cli' }]
})
```

- 连接字符串示例
  - 无鉴权（本地开发最简单）：`mongodb://127.0.0.1:27017`
  - 有鉴权：`*******************************************************************************`

说明：使用 `MongoDBSaver` 时，首次写入会自动创建集合；如需自定义集合名可通过参数覆盖。

---

### 2) 在 createLangChainCLI 中启用 MongoDB Checkpointer

入口函数名：`createLangChainCLI`（你已从 `createLangChainGeminiCLI` 重命名）。

最小可用示例（无鉴权）：
```ts
import { createLangChainCLI } from '@google/gemini-cli-lang';
import { ExtendedAuthType } from '@google/gemini-cli-lang/dist/types';

const cli = await createLangChainCLI({
  sessionId: `hai-code-${Date.now()}`,
  model: 'ht::saas-deepseek-v3',
  targetDir: process.cwd(),
  cwd: process.cwd(),
  debugMode: true,
  question: '',
  fullContext: false,
  userMemory: '',
  geminiMdFileCount: 50,
  telemetry: { enabled: false },

  // 供应商示例（也可 USE_GEMINI/USE_ANTHROPIC 等）
  authType: ExtendedAuthType.USE_OPENAI_COMPATIBLE,
  baseURL: process.env.OPENAI_BASE_URL, // 例：'http://localhost:11434/v1'

  // 可选：MongoDB Checkpointer（不配则逻辑不变）
  langGraphMongo: {
    uri: 'mongodb://127.0.0.1:27017',
    dbName: 'gemini_cli',
    checkpointCollectionName: 'checkpoints',
    checkpointWritesCollectionName: 'checkpoint_writes'
  },
});
```

如使用鉴权用户，请将 `uri` 替换为：
```
*******************************************************************************
```

最小化配置（集合名使用默认）：
```ts
langGraphMongo: { uri: 'mongodb://127.0.0.1:27017', dbName: 'gemini_cli' }
```

> 依赖：需要在上层项目安装 `@langchain/langgraph-checkpoint-mongodb` 与 `mongodb`。

---

### 3) 可选：接入 Langfuse 回调

如需在 LangChain/LangGraph 的执行链路中启用追踪，可添加 `langfuse` 配置；未配置则逻辑不变。

```ts
import { createLangChainCLI } from '@google/gemini-cli-lang';

const cli = await createLangChainCLI({
  // ...其余参数同上...
  langfuse: {
    enabled: true,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    baseUrl: process.env.LANGFUSE_BASEURL || 'https://cloud.langfuse.com',
    userId: 'user-123',           // 可选
    sessionId: 'sess-abc',        // 可选
    release: '1.0.0',             // 可选
    version: 'build-2025-01-01',  // 可选
    updateRoot: false,            // 可选
  }
});
```

> 依赖：需要在上层项目安装 `langfuse-langchain`。

常见环境变量（按供应商选择）：
- OpenAI 兼容：`OPENAI_API_KEY` 与 `OPENAI_BASE_URL`
- Gemini：`GEMINI_API_KEY`
- Anthropic：`ANTHROPIC_API_KEY`

---

### 4) 验证与排错

- 验证 MongoDB 已写入检查点：
```bash
mongosh
use gemini_cli
show collections
db.checkpoints.findOne()
db.checkpoint_writes.findOne()
```

- 常见问题：
  - 端口占用：修改 `mongod` 端口或停止冲突实例。
  - 连接被拒绝：确认 `brew services start mongodb-community@7.0` 已启动，`mongosh` 可连。
  - 权限不足：使用 `db.createUser` 创建 `readWrite` 角色，并确保 `authSource` 指向创建用户的 DB。

---

### 5) 行为开关说明

- `langGraphMongo`、`langfuse` 均为可选；未配置时保持原有逻辑不变。
- 仅当提供对应配置时，才会自动启用 MongoDB Checkpointer 或 Langfuse 回调。


