{"compilerOptions": {"strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "composite": true, "incremental": true, "declaration": true, "allowSyntheticDefaultImports": true, "lib": ["ES2023"], "module": "NodeNext", "moduleResolution": "nodenext", "target": "es2022", "types": ["node", "vitest/globals"], "jsx": "react-jsx"}}