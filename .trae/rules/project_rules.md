1. 当前项目使用的框架版本、依赖
   - langchainjs
   - @langchain/langgraph
2. 对langchain和langgraph的api不清楚时请查阅：
   - https://js.langchain.com/docs/introduction/
   - https://deepwiki.com/langchain-ai/langchainjs/1.2-development-and-build-system
   - https://deepwiki.com/langchain-ai/langchainjs/2.1-runnable-system
3. 实现代码时请不要使用any、unknown类型，因为这容易带来类型安全和逻辑错误问题。
4. packages/lang是对packages/core的逻辑基于langchainjs和langgraph的二次实现，核心逻辑一致
