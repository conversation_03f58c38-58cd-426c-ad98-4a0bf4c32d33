# 开发

### Pull Request 指南

为了帮助我们快速审查和合并您的 PR，请遵循以下指南。不符合这些标准的 PR 可能会被关闭。

#### 1. 关联现有问题

所有 PR 都应该关联一个现有问题。这确保了每个更改都经过讨论并与项目目标保持一致，在编写代码之前。

- **对于错误修复:** PR 应该关联错误报告问题。
- **对于功能:** PR 应该关联功能请求或提案问题，该问题已由维护者批准。

如果您的更改没有问题，请 **先打开一个** 并等待反馈后再开始编码。

#### 2. 保持小而专注

我们更喜欢小而原子的 PR，解决单个问题或添加单个自包含功能。

- **做:** 创建一个修复特定错误或添加特定功能的 PR。
- **不做:** 将多个不相关的更改（例如错误修复、新功能和重构）捆绑到一个 PR 中。

大型更改应该分解为一系列更小、逻辑清晰的 PR，可以独立审查和合并。

#### 3. 使用草稿 PR 进行工作

如果您想尽早获得反馈，请使用 GitHub 的 **草稿 PR** 功能。这向维护者表明 PR 尚未准备好正式审查，但可以进行讨论和初始反馈。

#### 4. 确保所有检查通过

在提交 PR 之前，请确保所有自动检查都通过运行 `npm run preflight`。此命令运行所有测试、linting 和其他样式检查。

#### 5. 更新文档

如果您的 PR 引入用户可见的变化（例如新命令、修改的标志或行为变化），您还必须更新 `/docs` 目录中的相关文档。

#### 6. 编写清晰的提交消息和良好的 PR 描述

您的 PR 应该有一个清晰、描述性的标题和详细的更改描述。遵循 [Conventional Commits](https://www.conventionalcommits.org/) 标准进行提交消息。

- **好的 PR 标题:** `feat(cli): 添加 --json 标志到 'config get' 命令`
- **不好的 PR 标题:** `Made some changes`

在 PR 描述中，解释更改背后的“为什么”并链接到相关问题（例如 `Fixes #123`）。

## 分叉

如果您分叉了仓库，您将能够运行构建、测试和集成测试工作流。但是为了使集成测试运行，您需要添加一个 [GitHub 存储库秘密](https://docs.github.com/en/actions/security-for-github-actions/security-guides/using-secrets-in-github-actions#creating-secrets-for-a-repository)，其值为 `GEMINI_API_KEY`，并将其设置为有效的 API 密钥，您可以访问。您的密钥和秘密是私有的；没有访问权限的人看不到您的密钥，您也无法看到与此存储库相关的任何秘密。

此外，您需要单击 `Actions` 选项卡并启用存储库的工作流，您会发现它是屏幕中央的蓝色大按钮。

## 开发设置和工作流

本节指导贡献者如何构建、修改和理解此项目的开发设置。

### 设置开发环境

**先决条件:**

1.  **Node.js**:
    - **开发:** 请使用 Node.js `~20.19.0`。由于上游开发依赖问题，需要使用特定版本。您可以使用 [nvm](https://github.com/nvm-sh/nvm) 等工具管理 Node.js 版本。
    - **生产:** 在生产环境中运行 CLI，任何版本为 Node.js `>=20` 的版本都适用。
2.  **Git**

### 构建过程

要克隆仓库:

```bash
git clone https://github.com/google-gemini/gemini-cli.git # 或 fork 的 URL
cd gemini-cli
```

要安装 `package.json` 中定义的依赖以及根依赖:

```bash
npm install
```

要构建整个项目（所有包）:

```bash
npm run build
```

此命令通常将 TypeScript 编译为 JavaScript，捆绑资产，并为执行准备包。有关构建期间发生的情况的更多详细信息，请参阅 `scripts/build.js` 和 `package.json` 脚本。

### 启用沙盒

[沙盒](#沙盒) 是高度推荐的，至少需要设置 `GEMINI_SANDBOX=true` 在您的 `~/.env` 中，并确保沙盒提供者（例如 `macOS Seatbelt`、`docker` 或 `podman`）可用。有关详细信息，请参阅 [沙盒](#沙盒)。

要构建 `gemini` CLI 实用程序和沙盒容器，请从根目录运行 `build:all`:

```bash
npm run build:all
```

要跳过构建沙盒容器，您可以使用 `npm run build` 代替。

### 运行

要启动 Gemini CLI（在构建后），请从根目录运行以下命令:

```bash
npm start
```

如果您想在 gemini-cli 文件夹之外运行源构建，您可以使用 `npm link path/to/gemini-cli/packages/cli`（参见：[docs](https://docs.npmjs.com/cli/v9/commands/npm-link)）或 `alias gemini="node path/to/gemini-cli/packages/cli"` 来运行 `gemini`。

### 运行测试

该项目包含两种类型的测试：单元测试和集成测试。

#### 单元测试

要执行项目的单元测试套件:

```bash
npm run test
```

这将运行位于 `packages/core` 和 `packages/cli` 目录中的测试。确保在提交任何更改之前测试通过。为了更全面的检查，建议运行 `npm run preflight`。

#### 集成测试

集成测试旨在验证 Gemini CLI 的端到端功能。它们不是作为默认 `npm run test` 命令的一部分运行的。

要运行集成测试，请使用以下命令:

```bash
npm run test:e2e
```

有关集成测试框架的更多详细信息，请参阅 [集成测试文档](./docs/integration-tests.md)。

### 代码风格和预检检查

要确保代码质量和格式一致性，请运行预检检查:

```bash
npm run preflight
```

此命令将运行 ESLint、Prettier、所有测试和其他检查，如项目 `package.json` 中定义的。

_ProTip_ 

在克隆后创建一个 git precommit 钩子文件，以确保您的提交始终干净。

```bash
echo "
# Run npm build and check for errors
if ! npm run preflight; then
  echo "npm build failed. Commit aborted."
  exit 1
fi
" > .git/hooks/pre-commit && chmod +x .git/hooks/pre-commit
```

#### 格式化

要分别格式化此项目中的代码，请从根目录运行以下命令:

```bash
npm run format
```

此命令使用 Prettier 根据项目样式指南格式化代码。

#### 代码风格检查

要分别检查此项目中的代码，请从根目录运行以下命令:

```bash
npm run lint
```

### 代码约定

- 请遵循现有代码库中使用的代码风格、模式和约定。
- 有关 AI 辅助开发的特定说明，包括 React、注释和 Git 使用约定，请参阅 [GEMINI.md](https://github.com/google-gemini/gemini-cli/blob/main/GEMINI.md)（通常位于项目根目录）。
- **Imports:** Pay special attention to import paths. The project uses ESLint to enforce restrictions on relative imports between packages.

### 项目结构

- `packages/`: 包含项目的各个子包。
  - `cli/`: 命令行界面。
  - `core/`: Gemini CLI 的核心后端逻辑。
- `docs/`: 包含所有项目文档。
- `scripts/`: 用于构建、测试和开发任务的实用脚本。

有关更详细的架构，请参阅 `docs/architecture.md`。

## 调试

### VS Code:

0.  使用 `F5` 在 VS Code 中交互式调试 CLI
1.  从根目录启动 CLI 调试模式:
    ```bash
    npm run debug
    ```
    此命令在 `packages/cli` 目录中运行 `node --inspect-brk dist/gemini.js`，暂停执行，直到调试器附加。然后您可以在 Chrome 浏览器中打开 `chrome://inspect` 以连接到调试器。
2.  在 VS Code 中，使用 "Attach" 启动配置（位于 `.vscode/launch.json`）。

或者，如果您更喜欢直接启动当前打开的文件，您可以使用 VS Code 中的 "Launch Program" 配置，但 'F5' 通常是推荐的。

要命中沙盒容器内的断点，请运行:

```bash
DEBUG=1 gemini
```

### React DevTools

要调试 CLI 的基于 React 的 UI，您可以使用 React DevTools。Ink，用于 CLI 的库，与 React DevTools 版本 4.x 兼容。

1.  **在开发模式下启动 Gemini CLI:**

    ```bash
    DEV=true npm start
    ```

2.  **安装并运行 React DevTools 版本 4.28.5（或最新兼容的 4.x 版本）:**

    您可以全局安装它:

    ```bash
    npm install -g react-devtools@4.28.5
    react-devtools
    ```

    或者直接使用 npx 运行:

    ```bash
    npx react-devtools@4.28.5
    ```

    您的运行 CLI 应用程序应该连接到 React DevTools。
    ![](/docs/assets/connected_devtools.png)

## 沙盒

### macOS Seatbelt

在 macOS 上，`gemini` 使用 `permissive-open` 配置文件（见 `packages/cli/src/utils/sandbox-macos-permissive-open.sb`），该配置文件默认限制对项目文件夹的写入，但允许所有其他操作和出站网络流量（“开放”）。您可以切换到 `restrictive-closed` 配置文件（见 `packages/cli/src/utils/sandbox-macos-restrictive-closed.sb`），该配置文件默认拒绝所有操作和出站网络流量（“关闭”），通过在您的环境或 `.env` 文件中设置 `SEATBELT_PROFILE=restrictive-closed`。内置配置文件包括 `{permissive,restrictive}-{open,closed,proxied}`（有关代理网络的详细信息，请参见下文）。如果您还在项目设置目录 `.gemini` 下创建了文件 `.gemini/sandbox-macos-<profile>.sb`，您还可以切换到自定义配置文件 `SEATBELT_PROFILE=<profile>`。

### 基于容器的沙盒（所有平台）

对于 macOS 或其他平台上的更强容器沙盒，您可以在环境或 `.env` 文件中设置 `GEMINI_SANDBOX=true|docker|podman|<command>`。指定的命令（或如果 `true` 则 `docker` 或 `podman`）必须在主机机器上安装。启用后，`npm run build:all` 将构建一个最小容器（“沙盒”）镜像，`npm start` 将在该容器的新实例中启动。第一次构建可能需要 20-30 秒（主要是由于下载基础镜像），但之后构建和启动开销应该最小。默认构建（`npm run build`）不会重新构建沙盒。

基于容器的沙盒将项目目录（和系统临时目录）挂载为读写访问，并在您启动/停止 Gemini CLI 时自动启动/停止/删除。沙盒内创建的文件应自动映射到主机机器上的您的用户/组。您可以根据需要设置 `SANDBOX_{MOUNTS,PORTS,ENV}` 来轻松指定其他挂载、端口或环境变量。您还可以通过在项目设置目录（`.gemini`）下创建文件 `.gemini/sandbox.Dockerfile` 和/或 `.gemini/sandbox.bashrc` 并使用 `BUILD_SANDBOX=1` 运行 `gemini` 来完全自定义沙盒。

#### 代理网络

所有沙盒方法，包括使用 `*-proxied` 配置文件的 macOS Seatbelt，都支持通过自定义代理服务器限制出站网络流量，该代理服务器可以指定为 `GEMINI_SANDBOX_PROXY_COMMAND=<command>`，其中 `<command>` 必须启动一个代理服务器，该代理服务器监听 `:::8877` 以处理相关请求。有关最小代理的详细信息，请参阅 `docs/examples/proxy-script.md`，该代理仅允许 `HTTPS` 连接到 `example.com:443`（例如 `curl https://example.com`）并拒绝所有其他请求。代理与沙盒自动启动和停止。

## 手动发布

我们为每个提交发布一个工件到内部注册表。但是如果您需要手动构建本地版本，请运行以下命令:

```
npm run clean
npm install
npm run auth
npm run prerelease:dev
npm publish --workspaces
```
