// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<ModelStatsDisplay /> > should display a single model correctly 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Model Stats For Nerds                                                                           │
│                                                                                                  │
│  Metric                      gemini-2.5-pro                                                      │
│  ──────────────────────────────────────────────────────────────────────────────────────────────  │
│  API                                                                                             │
│  Requests                    1                                                                   │
│  Errors                      0 (0.0%)                                                            │
│  Avg Latency                 100ms                                                               │
│                                                                                                  │
│  Tokens                                                                                          │
│  Total                       30                                                                  │
│    ↳ Prompt                  10                                                                  │
│    ↳ Cached                  5 (50.0%)                                                           │
│    ↳ Thoughts                2                                                                   │
│    ↳ Tool                    1                                                                   │
│    ↳ Output                  20                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<ModelStatsDisplay /> > should display conditional rows if at least one model has data 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Model Stats For Nerds                                                                           │
│                                                                                                  │
│  Metric                      gemini-2.5-pro        gemini-2.5-flash                              │
│  ──────────────────────────────────────────────────────────────────────────────────────────────  │
│  API                                                                                             │
│  Requests                    1                     1                                             │
│  Errors                      0 (0.0%)              0 (0.0%)                                      │
│  Avg Latency                 100ms                 50ms                                          │
│                                                                                                  │
│  Tokens                                                                                          │
│  Total                       30                    15                                            │
│    ↳ Prompt                  10                    5                                             │
│    ↳ Cached                  5 (50.0%)             0 (0.0%)                                      │
│    ↳ Thoughts                2                     0                                             │
│    ↳ Tool                    0                     3                                             │
│    ↳ Output                  20                    10                                            │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<ModelStatsDisplay /> > should display stats for multiple models correctly 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Model Stats For Nerds                                                                           │
│                                                                                                  │
│  Metric                      gemini-2.5-pro        gemini-2.5-flash                              │
│  ──────────────────────────────────────────────────────────────────────────────────────────────  │
│  API                                                                                             │
│  Requests                    10                    20                                            │
│  Errors                      1 (10.0%)             2 (10.0%)                                     │
│  Avg Latency                 100ms                 25ms                                          │
│                                                                                                  │
│  Tokens                                                                                          │
│  Total                       300                   600                                           │
│    ↳ Prompt                  100                   200                                           │
│    ↳ Cached                  50 (50.0%)            100 (50.0%)                                   │
│    ↳ Thoughts                10                    20                                            │
│    ↳ Tool                    5                     10                                            │
│    ↳ Output                  200                   400                                           │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<ModelStatsDisplay /> > should handle large values without wrapping or overlapping 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Model Stats For Nerds                                                                           │
│                                                                                                  │
│  Metric                      gemini-2.5-pro                                                      │
│  ──────────────────────────────────────────────────────────────────────────────────────────────  │
│  API                                                                                             │
│  Requests                    999,999,999                                                         │
│  Errors                      123,456,789 (12.3%)                                                 │
│  Avg Latency                 0ms                                                                 │
│                                                                                                  │
│  Tokens                                                                                          │
│  Total                       999,999,999                                                         │
│    ↳ Prompt                  987,654,321                                                         │
│    ↳ Cached                  123,456,789 (12.5%)                                                 │
│    ↳ Thoughts                111,111,111                                                         │
│    ↳ Tool                    222,222,222                                                         │
│    ↳ Output                  123,456,789                                                         │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<ModelStatsDisplay /> > should not display conditional rows if no model has data for them 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Model Stats For Nerds                                                                           │
│                                                                                                  │
│  Metric                      gemini-2.5-pro                                                      │
│  ──────────────────────────────────────────────────────────────────────────────────────────────  │
│  API                                                                                             │
│  Requests                    1                                                                   │
│  Errors                      0 (0.0%)                                                            │
│  Avg Latency                 100ms                                                               │
│                                                                                                  │
│  Tokens                                                                                          │
│  Total                       30                                                                  │
│    ↳ Prompt                  10                                                                  │
│    ↳ Output                  20                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<ModelStatsDisplay /> > should render "no API calls" message when there are no active models 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  No API calls have been made in this session.                                                    │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;
