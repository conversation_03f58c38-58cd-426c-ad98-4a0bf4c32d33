// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<RadioButtonSelect /> > renders a list of items and matches snapshot 1`] = `
"● 1. Option 1
  2. Option 2
  3. Option 3"
`;

exports[`<RadioButtonSelect /> > renders a list with >10 items and matches snapshot 1`] = `
"●  1. Item 1
   2. Item 2
   3. Item 3
   4. Item 4
   5. Item 5
   6. Item 6
   7. Item 7
   8. Item 8
   9. Item 9
  10. Item 10"
`;

exports[`<RadioButtonSelect /> > renders with numbers hidden and matches snapshot 1`] = `
"● 1. Option 1
  2. Option 2
  3. Option 3"
`;

exports[`<RadioButtonSelect /> > renders with scroll arrows and matches snapshot 1`] = `
"▲
●  1. Item 1
   2. Item 2
   3. Item 3
   4. Item 4
   5. Item 5
▼"
`;

exports[`<RadioButtonSelect /> > renders with special theme display and matches snapshot 1`] = `
"● 1. Theme A (Light)
  2. Theme B (Dark)"
`;

exports[`<RadioButtonSelect /> > renders with the second item selected and matches snapshot 1`] = `
"  1. Option 1
● 2. Option 2
  3. Option 3"
`;
