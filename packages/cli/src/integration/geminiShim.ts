import type { ServerGeminiStreamEvent, ServerGeminiFinishedEvent, ServerGeminiContentEvent } from '@google/gemini-cli-core';
import type { Content, PartListUnion } from '@google/genai';
import { GeminiEventType } from '@google/gemini-cli-core';
import { FinishReason } from '@google/genai';
import { extractParts, partsToText } from './utils/request.js';

/**
 * Minimal GeminiClient shim backed by packages/lang agent
 * - Emits only content/finished events (no tool events)
 * - Keeps simple in-memory history compatible with UI usage
 */
export class GeminiClientShim {
  private history: Content[] = [];

  constructor(
    private readonly lcAgent: {
      streamMessage: (
        msg: string,
        sessionId?: string,
        userMemory?: string,
      ) => AsyncGenerator<string>;
    },
    private readonly sessionId: string,
  ) {}

  isInitialized(): boolean {
    return true;
  }

  async *sendMessageStream(
    request: PartListUnion,
    _signal: AbortSignal,
    _prompt_id: string,
  ): AsyncGenerator<ServerGeminiStreamEvent> {
    const userText = partsToText(extractParts(request));

    // Debug logging
    console.debug('[GeminiClientShim] Received request:', JSON.stringify(request, null, 2));
    console.debug('[GeminiClientShim] Extracted userText:', JSON.stringify(userText));

    // Validate user input
    if (!userText || userText.trim().length === 0) {
      console.error('[GeminiClientShim] Empty user message received');
      const errorEvent: ServerGeminiContentEvent = {
        type: GeminiEventType.Content,
        value: 'Error: Empty message received. Please type a message.',
      } as ServerGeminiContentEvent;
      yield errorEvent;

      const finished: ServerGeminiFinishedEvent = {
        type: GeminiEventType.Finished,
        value: FinishReason.STOP,
      } as ServerGeminiFinishedEvent;
      yield finished;
      return;
    }

    let finalText = '';
    try {
      for await (const chunk of this.lcAgent.streamMessage(
        userText.trim(),
        this.sessionId,
      )) {
        finalText += chunk;
        const evt: ServerGeminiContentEvent = {
          type: GeminiEventType.Content,
          value: chunk,
        } as ServerGeminiContentEvent;
        yield evt;
      }
    } catch (error) {
      console.error('[GeminiClientShim] Error in streamMessage:', error);
      const errorEvent: ServerGeminiContentEvent = {
        type: GeminiEventType.Content,
        value: `Error: ${error instanceof Error ? error.message : String(error)}`,
      } as ServerGeminiContentEvent;
      yield errorEvent;
    }

    const finished: ServerGeminiFinishedEvent = {
      type: GeminiEventType.Finished,
      value: FinishReason.STOP,
    } as ServerGeminiFinishedEvent;
    this.addHistory({ role: 'user', parts: [{ text: userText }] });
    this.addHistory({ role: 'model', parts: [{ text: finalText }] });
    yield finished;
  }

  addHistory(content: Content): void {
    this.history.push(content);
  }

  async setHistory(history: Content[]): Promise<void> {
    this.history = Array.isArray(history) ? history.slice() : [];
  }

  async getHistory(): Promise<Content[]> {
    return this.history.slice();
  }

  getUserTier(): unknown {
    return undefined;
  }

  getContentGenerator(): unknown {
    // Not available in LangChain shim
    return undefined;
  }

  async getChat(): Promise<{
    getHistory: () => Content[];
    sendMessageStream: (
      req: PartListUnion,
      _signal: AbortSignal,
      prompt_id: string,
    ) => AsyncGenerator<ServerGeminiStreamEvent>;
  }> {
    return {
      getHistory: () => this.history.slice(),
      sendMessageStream: (req, signal, prompt_id) =>
        this.sendMessageStream(req, signal, prompt_id),
    };
  }
}
