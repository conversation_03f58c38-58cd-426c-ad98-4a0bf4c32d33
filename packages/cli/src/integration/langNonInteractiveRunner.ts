import type { Config } from '@google/gemini-cli-core';
import { createHaicodeAgent } from '@ht/hai-code-cli';
import { getAuthTypeFromEnv } from './utils/auth.js';
import { getModel } from './utils/model.js';

/**
 * 非交互模式下，使用 packages/lang 的实现完成一次问答并流式输出。
 * 仅在环境变量 GEMINI_CLI_USE_LANGCHAIN === 'true' 时被调用。
 *
 * 采用与 UI 互动模式相同的轻量 Shim 方式，尽量不入侵原有 CLI 流程。
 */
export async function runNonInteractiveLang(
  config: Config,
  input: string,
  _prompt_id: string,
): Promise<void> {
  try {
    // 从环境变量确定认证类型和模型，绕过 core 包的限制
    const authType = getAuthTypeFromEnv();

    // 确定模型名称，优先使用环境变量，然后根据认证类型选择合适的模型
    const model = getModel(authType, config.getModel());

    // 构造与交互式保持一致的参数对齐
    const lcAgent = await createHaicodeAgent({
      sessionId: config.getSessionId(),
      targetDir: config.getTargetDir() ?? process.cwd(),
      debugMode: config.getDebugMode(),
      question: '',
      fullContext: config.getFullContext() ?? false,
      userMemory: config.getUserMemory() ?? '',
      geminiMdFileCount: config.getGeminiMdFileCount() ?? 0,
      cwd: process.cwd(),
      model,
      checkpointer: config.getCheckpointingEnabled() ?? false,
      authType,
    });

    // 直接流式输出
    const stream = lcAgent.streamMessage(input, config.getSessionId());
    for await (const chunk of stream) {
      process.stdout.write(chunk);
    }
    process.stdout.write('\n');
  } catch (error) {
    console.error('Error in LangChain non-interactive mode:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}


