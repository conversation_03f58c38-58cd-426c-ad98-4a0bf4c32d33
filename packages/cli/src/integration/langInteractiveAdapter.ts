import type { Config } from '@google/gemini-cli-core';
import { createHaicodeAgent, setupLangChainEnvironment } from '@ht/hai-code-cli';
import { getAuthTypeFromEnv } from './utils/auth.js';
import { getModel } from './utils/model.js';
import { GeminiClientShim } from './geminiShim.js';
import { useHaiCode } from './config.js';

/**
 * 适配配置，使交互式 UI 使用 LangChain 实现
 * @param config 配置
 * @param argv 命令行参数
 */
export async function adaptConfigForLangInteractive(
  config: Config,
  argv: { checkpointing?: boolean } = {},
): Promise<void> {
  if (!useHaiCode) return;

  try {
    if (typeof setupLangChainEnvironment === 'function') {
      setupLangChainEnvironment();
    }

    // 从环境变量确定认证类型和模型，与非交互模式保持一致
    const authType = getAuthTypeFromEnv();
    const model = getModel(authType, config.getModel());

    const lcAgent = await createHaicodeAgent({
      sessionId: config.getSessionId(),
      targetDir: config.getTargetDir() ?? process.cwd(),
      debugMode: config.getDebugMode(),
      question: '',
      fullContext: config.getFullContext() ?? false,
      userMemory: config.getUserMemory() ?? '',
      geminiMdFileCount: config.getGeminiMdFileCount() ?? 0,
      cwd: process.cwd(),
      model,
      // Tooling & server integration parity
      coreTools: config.getCoreTools(),
      excludeTools: config.getExcludeTools(),
      toolDiscoveryCommand: config.getToolDiscoveryCommand(),
      toolCallCommand: config.getToolCallCommand(),
      mcpServerCommand: config.getMcpServerCommand(),
      mcpServers: config.getMcpServers(),
      approvalMode: config.getApprovalMode(),
      checkpointer: !!argv.checkpointing,
      // 添加认证相关参数
      authType,
    });

    const shim = new GeminiClientShim(lcAgent, config.getSessionId());

    // Monkey-patch getGeminiClient to return shim for UI
    config.getGeminiClient = () => shim as unknown as never;

    // Prevent core auth flows from running in LangChain mode
    config.refreshAuth = async () => undefined as unknown as never;
    // config.getContentGenerator = () => undefined as unknown as never;

    // Ensure UI model display is compatible with LangChain auth choice
    const currentModel = config.getModel();
    if (currentModel && currentModel.startsWith('gemini-')) {
      // Override getter to avoid incompatible model in status bar
      config.getModel = () => 'ht::saas-deepseek-v3' as unknown as never;
    }
  } catch (err) {
    if (config.getDebugMode()) {
      console.warn('[LangChain] Interactive adapter disabled due to error:', err);
    }
  }
}


