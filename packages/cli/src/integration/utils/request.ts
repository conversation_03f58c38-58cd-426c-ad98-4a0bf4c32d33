import type { Part, PartListUnion } from '@google/genai';

export function extractParts(req: PartListUnion): Part[] {
  if (!req) return [];

  // Handle string input - convert to text part
  if (typeof req === 'string') {
    return [{ text: req }];
  }

  // PartListUnion can be an array of Part or an object with message: Part[]
  if (Array.isArray(req)) {
    return req as Part[];
  }

  // Check for various possible structures
  const asObj = req as unknown as Record<string, unknown>;

  // Try message property
  const maybeParts = asObj.message as Part[] | undefined;
  if (Array.isArray(maybeParts)) {
    return maybeParts as Part[];
  }

  // Try parts property
  const maybePartsProperty = asObj.parts as Part[] | undefined;
  if (Array.isArray(maybePartsProperty)) {
    return maybePartsProperty as Part[];
  }

  // Try content property
  const maybeContent = asObj.content as Part[] | undefined;
  if (Array.isArray(maybeContent)) {
    return maybeContent as Part[];
  }

  // If it's a single Part object, wrap it in an array
  if (asObj.text !== undefined) {
    return [asObj as Part];
  }

  return [];
}

export function partsToText(parts: Part[]): string {
  const result: string[] = [];
  for (const p of parts) {
    if ((p as unknown as { text?: unknown })?.text) {
      result.push(String((p as unknown as { text?: unknown }).text));
    }
  }
  return result.join('');
}
