/**
 * 获取认证类型
 */
export function getAuthTypeFromEnv() {
  const authType = process.env.OPENAI_API_KEY || process.env.OPENAI_BASE_URL
  ? 'USE_OPENAI_COMPATIBLE'
  : process.env.ANTHROPIC_API_KEY
    ? 'USE_ANTHROPIC'
    : process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true'
      ? 'USE_VERTEX_AI'
      : process.env.GEMINI_API_KEY
        ? 'USE_GEMINI'
        : 'USE_OPENAI_COMPATIBLE';
  return authType;
}
