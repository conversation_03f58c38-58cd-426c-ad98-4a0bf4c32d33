const DEFAULT_HT_MODEL = 'ht::saas-deepseek-v3';

/**
 * 获取模型名称
 * @param authType 认证类型
 * @param configModel 配置中的模型
 * @returns 模型名称
 */
export function getModel(authType: string, configModel?: string) {
  if (configModel && (
    (authType === 'USE_OPENAI_COMPATIBLE' && (configModel.startsWith('ht::'))) ||
    (authType === 'USE_ANTHROPIC' && configModel.startsWith('claude-')) ||
    (authType === 'USE_GEMINI' && configModel.startsWith('gemini-'))
  )) {
    return configModel;
  } else if (process.env.GEMINI_MODEL) {
    return process.env.GEMINI_MODEL;
  } else  {
    // 使用与认证类型匹配的默认模型
    return authType === 'USE_OPENAI_COMPATIBLE' ? DEFAULT_HT_MODEL :
            authType === 'USE_ANTHROPIC' ? 'claude-3-5-sonnet-20241022' :
            'gemini-1.5-flash';
  }
}
