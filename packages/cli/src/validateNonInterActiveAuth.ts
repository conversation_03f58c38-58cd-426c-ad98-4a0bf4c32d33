/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { AuthType, Config } from '@google/gemini-cli-core';
import { USER_SETTINGS_PATH } from './config/settings.js';
import { validateAuth<PERSON>ethod } from './config/auth.js';
import { useHaiCode } from './integration/config.js';
import { getAuthTypeFromEnv } from './integration/utils/auth.js';

export async function validateNonInteractiveAuth(
  configuredAuthType: AuthType | string | undefined,
  nonInteractiveConfig: Config,
) {
  const effectiveAuthType =
    configuredAuthType ||
    (useHaiCode
      ? (getAuthTypeFromEnv())
      : (process.env.GOOGLE_GENAI_USE_VERTEXAI === 'true'
          ? AuthType.USE_VERTEX_AI
          : process.env.GEMINI_API_KEY
            ? AuthType.USE_GEMINI
            : undefined));

  if (!effectiveAuthType) {
    console.error(
      useHaiCode
        ? `Please set an Auth method in your ${USER_SETTINGS_PATH} or specify one of: (OPENAI_API_KEY & OPENAI_BASE_URL), ANTHROPIC_API_KEY, GOOGLE_GENAI_USE_VERTEXAI, or GEMINI_API_KEY.`
        : `Please set an Auth method in your ${USER_SETTINGS_PATH} or specify either the GEMINI_API_KEY or GOOGLE_GENAI_USE_VERTEXAI environment variables before running`,
    );
    process.exit(1);
  }

  const err = validateAuthMethod(effectiveAuthType);
  if (err != null) {
    console.error(err);
    process.exit(1);
  }

  await nonInteractiveConfig.refreshAuth(effectiveAuthType as AuthType);
  return nonInteractiveConfig;
}
