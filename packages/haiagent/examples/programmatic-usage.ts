/**
 * LangV2 Agent 编程式使用示例
 * 展示如何在 TypeScript 代码中使用 LangV2Agent 类
 */

import { LangV2Agent } from '../src/index.js';

async function basicUsageExample() {
  console.log('🔧 基本使用示例');
  console.log('================');

  // 创建 agent 实例
  const agent = new LangV2Agent({
    model: 'ht::saas-deepseek-v3',
    // 可选：自定义 API 配置
    // baseUrl: 'http://localhost:11434/v1',
    // apiKey: 'your-api-key',
  });

  // 单次问答
  const response = await agent.processMessage('什么是 TypeScript？');
  console.log('响应:', response);
  console.log('');
}

async function streamingExample() {
  console.log('🌊 流式输出示例');
  console.log('================');

  const agent = new LangV2Agent();

  console.log('问题: 解释一下什么是异步编程');
  console.log('流式响应:');
  
  // 流式处理
  for await (const chunk of agent.streamMessage('解释一下什么是异步编程')) {
    process.stdout.write(chunk);
  }
  
  console.log('\n');
}

async function customConfigExample() {
  console.log('⚙️ 自定义配置示例');
  console.log('==================');

  // 使用自定义配置
  const agent = new LangV2Agent({
    model: 'ht::saas-deepseek-v3',
    baseUrl: process.env.OPENAI_BASE_URL,
    apiKey: process.env.OPENAI_API_KEY,
    temperature: 0.7,
    maxTokens: 1000,
  });

  const response = await agent.processMessage('简单介绍一下 Node.js');
  console.log('响应:', response);
  console.log('');
}

async function errorHandlingExample() {
  console.log('🚨 错误处理示例');
  console.log('================');

  const agent = new LangV2Agent({
    model: 'invalid-model', // 故意使用无效模型
  });

  try {
    await agent.processMessage('测试错误处理');
  } catch (error) {
    console.log('捕获到错误:', error instanceof Error ? error.message : String(error));
  }
  console.log('');
}

async function main() {
  console.log('🤖 LangV2 Agent 编程式使用示例');
  console.log('================================\n');

  try {
    await basicUsageExample();
    await streamingExample();
    await customConfigExample();
    // await errorHandlingExample(); // 取消注释以测试错误处理
    
    console.log('✅ 所有示例执行完成！');
  } catch (error) {
    console.error('❌ 执行过程中出现错误:', error);
  }
}

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export {
  basicUsageExample,
  streamingExample,
  customConfigExample,
  errorHandlingExample,
};
