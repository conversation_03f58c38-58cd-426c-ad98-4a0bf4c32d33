# Langfuse 集成指南

Hai Agent 已集成 Langfuse 可观测性平台，支持自动追踪 LangChain 和 LangGraph 的执行过程。

## 功能特性

- ✅ 自动追踪 LLM 调用和工具使用
- ✅ 会话级别的追踪管理
- ✅ 用户身份识别
- ✅ 环境变量配置
- ✅ 优雅的错误处理和资源清理
- ✅ 支持启用/禁用切换

## 快速开始

### 1. 环境变量配置

复制示例配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置 Langfuse 参数：
```bash
# 启用 Langfuse
LANGFUSE_ENABLED=true

# Langfuse 服务器配置
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_BASEURL=https://cloud.langfuse.com
```

### 2. 获取 Langfuse 凭据

1. 访问 [Langfuse Cloud](https://cloud.langfuse.com) 或您的私有部署
2. 创建项目
3. 在项目设置中获取 Public Key 和 Secret Key

### 3. 使用 Hai Agent

正常使用 Hai Agent，Langfuse 追踪将自动启用：

```bash
# 单次对话
hai-agent "解释什么是机器学习"

# 交互模式
hai-agent --interactive

# 调试模式（显示 Langfuse 状态）
hai-agent --debug "你好"
```

## 配置选项

### 环境变量

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `LANGFUSE_ENABLED` | 否 | `true` | 启用/禁用 Langfuse |
| `LANGFUSE_PUBLIC_KEY` | 是* | - | Langfuse 公钥 |
| `LANGFUSE_SECRET_KEY` | 是* | - | Langfuse 私钥 |
| `LANGFUSE_BASEURL` | 否 | 测试环境 | Langfuse 服务器地址 |
| `LANGFUSE_RELEASE` | 否 | - | 应用版本标识 |
| `LANGFUSE_VERSION` | 否 | - | 构建版本标识 |

*当 `LANGFUSE_ENABLED=true` 时必需

### 程序化配置

```typescript
import { HaiAgent } from '@ht/hai-code-agent';

const agent = new HaiAgent({
  model: 'ht::saas-deepseek-v3',
  langfuse: {
    enabled: true,
    userId: 'user-123',
    sessionId: 'session-abc',
    release: '1.0.0',
    version: 'build-2025-01-01',
  },
});
```

## 追踪信息

Langfuse 将自动记录以下信息：

### 基本信息
- **用户ID**: 自动从系统用户信息获取
- **会话ID**: 每次运行自动生成唯一标识
- **时间戳**: 精确的执行时间记录

### LLM 调用
- 模型名称和参数
- 输入提示和输出响应
- Token 使用量和成本
- 响应时间和延迟

### 工具使用
- 工具名称和参数
- 执行结果和错误信息
- 嵌套工具调用链

### 会话流程
- 完整的对话历史
- 多轮对话的上下文关联
- 错误和异常追踪

## 最佳实践

### 1. 生产环境配置

```bash
# 使用环境变量而非硬编码
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
LANGFUSE_BASEURL=https://your-langfuse-instance.com
```

### 2. 开发环境

```bash
# 可以禁用 Langfuse 以提高开发速度
LANGFUSE_ENABLED=false
```

### 3. 调试和监控

```bash
# 使用调试模式查看 Langfuse 状态
hai-agent --debug "测试消息"
```

## 故障排除

### 常见问题

1. **Langfuse 连接失败**
   - 检查网络连接和防火墙设置
   - 验证 `LANGFUSE_BASEURL` 是否正确
   - 确认 Public Key 和 Secret Key 有效

2. **追踪数据缺失**
   - 确认 `LANGFUSE_ENABLED=true`
   - 检查控制台是否有错误信息
   - 验证 Langfuse 服务器状态

3. **性能影响**
   - Langfuse 使用异步发送，对性能影响很小
   - 如需完全禁用，设置 `LANGFUSE_ENABLED=false`

### 调试信息

启用调试模式查看详细信息：
```bash
hai-agent --debug "你的问题"
```

输出示例：
```
Debug mode enabled
Model: ht::saas-deepseek-v3
Base URL: default
Langfuse: enabled
Langfuse URL: https://cloud.langfuse.com
```

## 技术实现

### 架构概览

```
HaiAgent
├── LangfuseHelper (单例管理器)
│   ├── 配置管理
│   ├── 回调处理器创建
│   └── 资源清理
├── CallbackHandler (langfuse-langchain)
│   ├── LLM 调用追踪
│   ├── 工具使用追踪
│   └── 异步事件发送
└── StateGraph (LangGraph)
    ├── Agent 节点
    ├── Tools 节点
    └── 自动回调集成
```

### 关键组件

1. **LangfuseHelper**: 统一的 Langfuse 管理器
2. **CallbackHandler**: LangChain 官方回调处理器
3. **自动清理**: 程序退出时自动刷新事件

## 相关链接

- [Langfuse 官方文档](https://langfuse.com/docs)
- [LangChain 集成指南](https://langfuse.com/docs/integrations/langchain)
- [LangGraph 支持](https://langfuse.com/docs/integrations/langchain/example-python-langgraph)
