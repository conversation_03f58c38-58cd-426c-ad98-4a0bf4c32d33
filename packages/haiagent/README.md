# LangV2 Agent CLI

基于 LangChain.js 和 LangGraph 的 AI 编程助手命令行工具，支持流式输出和多种使用模式。

## 功能特性

- 🚀 **流式输出**: 实时显示 AI 响应，提供更好的用户体验
- 🛠️ **丰富的工具集**: 内置文件操作、shell 命令、搜索等工具
- 💬 **多种交互模式**: 支持交互式对话、单次问答和管道输入
- ⚙️ **灵活配置**: 支持自定义模型、API 端点等参数
- 🔧 **模块化设计**: 基于 LangChain.js 和 LangGraph 构建，易于扩展

## 安装

```bash
# 在项目根目录
npm install

# 构建 langv2 包
cd packages/langv2
npm run build
```

## 使用方法

### 基本用法

```bash
# 直接提问
node dist/cli.js "什么是 TypeScript？"

# 使用 -p 参数
node dist/cli.js -p "解释量子计算的基本原理"

# 管道输入
echo "修复这段代码" | node dist/cli.js
```

### 交互模式

```bash
# 启动交互模式
node dist/cli.js -i

# 或者不提供任何参数（默认进入交互模式）
node dist/cli.js
```

### 命令行参数

- `-m, --model <model>`: 指定使用的模型（默认: ht::saas-deepseek-v3）
- `-p, --prompt <prompt>`: 非交互模式下的提示词
- `-b, --base-url <url>`: LLM API 的基础 URL（也可使用 OPENAI_BASE_URL 环境变量）
- `-i, --interactive`: 强制启动交互模式
- `-d, --debug`: 启用调试模式
- `-h, --help`: 显示帮助信息
- `-v, --version`: 显示版本信息

### 环境变量

- `OPENAI_API_KEY`: OpenAI API 密钥
- `OPENAI_BASE_URL`: OpenAI 兼容的 API 基础 URL
- `LANGV2_MODEL`: 默认使用的模型

### 使用示例

```bash
# 使用特定模型
node dist/cli.js -m "ht::saas-deepseek-v3" "法国的首都是什么？"

# 使用自定义 API 端点
OPENAI_BASE_URL=http://localhost:11434/v1 node dist/cli.js -m llama2 "你好"

# 启用调试模式
node dist/cli.js -d -p "解释 JavaScript 闭包"

# 管道输入示例
cat code.js | node dist/cli.js -p "请帮我优化这段代码"
```

## 可用工具

LangV2 Agent 内置了以下工具：

- **文件操作**: 读取、写入、编辑文件
- **Shell 命令**: 执行系统命令
- **搜索功能**: 在文件中搜索内容
- **Glob 模式**: 文件路径匹配
- **内存工具**: 记忆和检索信息

## 开发

```bash
# 监听模式构建
npm run build:watch

# 运行测试
npm test

# 代码检查
npm run lint
```

## 注意事项

1. 确保已正确配置 API 密钥和端点
2. 某些工具可能需要特定的系统权限
3. 在生产环境中使用时，请注意 API 调用的成本和频率限制

## 许可证

Apache-2.0
