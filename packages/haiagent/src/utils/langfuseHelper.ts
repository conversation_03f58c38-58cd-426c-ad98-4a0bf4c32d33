/**
 * Langfuse 集成工具类
 * 提供统一的 Langfuse 回调处理器创建和管理
 */

import { CallbackHandler } from 'langfuse-langchain';
import { getLangfuseConfig, type LangfuseConfig } from '../config/langfuse.js';

// 简单的日志工具
const logger = {
  debug: (message: string, ...args: unknown[]) => console.debug(`[DEBUG] [LangfuseHelper] ${message}`, ...args),
  info: (message: string, ...args: unknown[]) => console.info(`[INFO] [LangfuseHelper] ${message}`, ...args),
  error: (message: string, ...args: unknown[]) => console.error(`[ERROR] [LangfuseHelper] ${message}`, ...args),
};

export class LangfuseHelper {
  private static instance: LangfuseHelper;
  private config: LangfuseConfig;
  private handlers: Map<string, CallbackHandler> = new Map();

  private constructor() {
    this.config = getLangfuseConfig();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LangfuseHelper {
    if (!LangfuseHelper.instance) {
      LangfuseHelper.instance = new LangfuseHelper();
    }
    return LangfuseHelper.instance;
  }

  /**
   * 检查 Langfuse 是否启用
   */
  public isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 获取配置
   */
  public getConfig(): LangfuseConfig {
    return { ...this.config };
  }

  /**
   * 创建 Langfuse 回调处理器
   * @param options 可选的配置覆盖
   * @returns CallbackHandler 实例或 null（如果未启用）
   */
  public createCallbackHandler(options: {
    userId?: string;
    sessionId?: string;
    release?: string;
    version?: string;
    updateRoot?: boolean;
  } = {}): CallbackHandler | null {
    if (!this.isEnabled()) {
      logger.debug('Langfuse 未启用，跳过回调处理器创建');
      return null;
    }

    try {
      const handlerConfig = {
        publicKey: this.config.publicKey,
        secretKey: this.config.secretKey,
        baseUrl: this.config.baseUrl,
        userId: options.userId || this.config.userId,
        sessionId: options.sessionId || this.config.sessionId,
        release: options.release || this.config.release,
        version: options.version || this.config.version,
        updateRoot: options.updateRoot ?? this.config.updateRoot,
      };

      // 过滤掉 undefined 值
      const filteredConfig = Object.fromEntries(
        Object.entries(handlerConfig).filter(([_, value]) => value !== undefined)
      );

      const handler = new CallbackHandler(filteredConfig);
      
      logger.debug('Langfuse 回调处理器创建成功', {
        userId: options.userId,
        sessionId: options.sessionId,
        baseUrl: this.config.baseUrl,
      });

      return handler;
    } catch (error) {
      logger.error('创建 Langfuse 回调处理器失败:', error);
      return null;
    }
  }

  /**
   * 获取或创建会话级别的回调处理器
   * @param sessionId 会话ID
   * @param userId 用户ID
   * @returns CallbackHandler 实例或 null
   */
  public getSessionHandler(sessionId: string, userId?: string): CallbackHandler | null {
    if (!this.isEnabled()) {
      return null;
    }

    const key = `${sessionId}-${userId || 'anonymous'}`;
    
    if (!this.handlers.has(key)) {
      const handler = this.createCallbackHandler({
        sessionId,
        userId,
      });
      
      if (handler) {
        this.handlers.set(key, handler);
      }
    }

    return this.handlers.get(key) || null;
  }

  /**
   * 清理指定会话的处理器
   * @param sessionId 会话ID
   * @param userId 用户ID
   */
  public clearSessionHandler(sessionId: string, userId?: string): void {
    const key = `${sessionId}-${userId || 'anonymous'}`;
    this.handlers.delete(key);
  }

  /**
   * 清理所有处理器
   */
  public clearAllHandlers(): void {
    this.handlers.clear();
  }

  /**
   * 刷新所有处理器（确保事件发送到 Langfuse）
   */
  public async flushAll(): Promise<void> {
    if (!this.isEnabled()) {
      return;
    }

    const flushPromises = Array.from(this.handlers.values()).map(async (handler) => {
      try {
        await handler.flushAsync();
      } catch (error) {
        logger.error('刷新 Langfuse 处理器失败:', error);
      }
    });

    await Promise.all(flushPromises);
  }

  /**
   * 关闭所有处理器
   */
  public async shutdownAll(): Promise<void> {
    if (!this.isEnabled()) {
      return;
    }

    const shutdownPromises = Array.from(this.handlers.values()).map(async (handler) => {
      try {
        await handler.shutdownAsync();
      } catch (error) {
        logger.error('关闭 Langfuse 处理器失败:', error);
      }
    });

    await Promise.all(shutdownPromises);
    this.clearAllHandlers();
  }

  /**
   * 更新配置（重新加载环境变量）
   */
  public updateConfig(): void {
    this.config = getLangfuseConfig();
    logger.info('Langfuse 配置已更新', {
      enabled: this.config.enabled,
      baseUrl: this.config.baseUrl,
    });
  }
}

/**
 * 便捷函数：获取 Langfuse 助手实例
 */
export function getLangfuseHelper(): LangfuseHelper {
  return LangfuseHelper.getInstance();
}

/**
 * 便捷函数：创建 Langfuse 回调处理器
 */
export function createLangfuseHandler(options: {
  userId?: string;
  sessionId?: string;
  release?: string;
  version?: string;
  updateRoot?: boolean;
} = {}): CallbackHandler | null {
  return getLangfuseHelper().createCallbackHandler(options);
}
