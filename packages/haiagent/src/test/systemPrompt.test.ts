import { describe, it, expect, beforeEach } from 'vitest';
import { HaiAgent } from '../index.js';
import { getHaiAgentSystemPrompt } from '../prompt.js';

describe('HaiAgent System Prompt', () => {
  let agent: HaiAgent;

  beforeEach(() => {
    agent = new HaiAgent({
      model: 'gpt-3.5-turbo',
      apiKey: 'test-key'
    });
  });

  it('should generate default system prompt with tool information', () => {
    const prompt = getHaiAgentSystemPrompt();
    
    expect(prompt).toContain('You are an interactive CLI agent');
    expect(prompt).toContain('Available Tools');
    expect(prompt).toContain('user_info');
    expect(prompt).toContain('始终以中文回复');
  });

  it('should include user memory in system prompt', () => {
    const userMemory = 'User prefers TypeScript and uses VS Code';
    const prompt = getHaiAgentSystemPrompt(userMemory);
    
    expect(prompt).toContain(userMemory);
    expect(prompt).toContain('---');
  });

  it('should include tool information in system prompt', () => {
    const tools = [
      { name: 'read_file', description: 'Read file contents' },
      { name: 'write_file', description: 'Write file contents' }
    ];
    const prompt = getHaiAgentSystemPrompt(undefined, tools);
    
    expect(prompt).toContain('## read_file');
    expect(prompt).toContain('Read file contents');
    expect(prompt).toContain('## write_file');
    expect(prompt).toContain('Write file contents');
  });

  it('should allow updating system prompt', () => {
    const customPrompt = 'Custom system prompt for testing';
    agent.updateSystemPrompt(customPrompt);
    
    const currentPrompt = agent.getCurrentSystemPrompt();
    expect(currentPrompt).toBe(customPrompt);
  });

  it('should allow updating user memory', () => {
    const userMemory = 'User likes clean code';
    agent.updateUserMemory(userMemory);
    
    const currentPrompt = agent.getCurrentSystemPrompt();
    expect(currentPrompt).toContain(userMemory);
  });

  it('should reset to default system prompt', () => {
    const customPrompt = 'Custom prompt';
    agent.updateSystemPrompt(customPrompt);
    
    // Verify custom prompt is set
    expect(agent.getCurrentSystemPrompt()).toBe(customPrompt);
    
    // Reset and verify default prompt is restored
    agent.resetSystemPrompt();
    const defaultPrompt = agent.getCurrentSystemPrompt();
    expect(defaultPrompt).toContain('You are an interactive CLI agent');
    expect(defaultPrompt).not.toBe(customPrompt);
  });

  it('should include environment information in system prompt', () => {
    const prompt = getHaiAgentSystemPrompt();
    
    expect(prompt).toContain('<user_info>');
    expect(prompt).toContain('OS version');
    expect(prompt).toContain('workspace');
  });
});
