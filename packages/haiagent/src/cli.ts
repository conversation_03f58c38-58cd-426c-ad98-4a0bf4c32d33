#!/usr/bin/env node

/**
 * Hai Agent CLI
 * A command-line interface for the Hai Agent with streaming support
 */

import chalk from 'chalk';
import { HaiAgent } from './index.js';
import { parseCliArguments } from './utils/parseArgs.js';
import { readStdin } from './utils/readStdin.js';
import { runInteractiveMode } from './utils/interactive.js';
import { getLangfuseHelper } from './utils/langfuseHelper.js';
import { getUserInfo } from './utils/user.js';
import { getUUID } from './utils/uuid.js';

function printHelp() {
  console.log(chalk.cyan(`
Hai Agent - AI Coding Assistant CLI

Usage:
  hai-agent [options] [prompt]
  echo "your question" | hai-agent

Options:
  -m, --model <model>     Model to use (default: ht::saas-deepseek-v3)
  -p, --prompt <prompt>   Prompt to process (non-interactive mode)
  -b, --base-url <url>    Base URL for LLM API (can also use OPENAI_BASE_URL env)
  -i, --interactive       Start in interactive mode (default if no prompt)
  -d, --debug             Enable debug mode
  -h, --help              Show this help message
  -v, --version           Show version

Environment Variables:
  OPENAI_API_KEY          OpenAI API key
  OPENAI_BASE_URL         OpenAI-compatible API base URL
  LANGV2_MODEL            Default model to use

  Langfuse (可观测性):
  LANGFUSE_PUBLIC_KEY     Langfuse 公钥
  LANGFUSE_SECRET_KEY     Langfuse 私钥
  LANGFUSE_BASEURL        Langfuse 服务器地址
  LANGFUSE_ENABLED        启用/禁用 Langfuse (true/false)

Examples:
  hai-agent "What is the capital of France?"
  hai-agent -m ht::saas-deepseek-v3 "Explain quantum computing"
  echo "Fix this code" | hai-agent
  OPENAI_BASE_URL=http://localhost:11434/v1 hai-agent -m llama2 "Hello"
`));
}

function printVersion() {
  console.log('hai-agent version 0.1.0');
}

async function main() {
  try {
    const { options, prompt } = await parseCliArguments();

    // Handle EPIPE errors when piping output to commands that close early (e.g., head)
    process.stdout.on('error', (err: NodeJS.ErrnoException) => {
      if (err && err.code === 'EPIPE') {
        process.exit(0);
      }
    });

    if (options.help) {
      printHelp();
      process.exit(0);
    }

    if (options.version) {
      printVersion();
      process.exit(0);
    }

    // 获取 Langfuse 状态信息
    const langfuseHelper = getLangfuseHelper();
    const langfuseConfig = langfuseHelper.getConfig();

    if (options.debug) {
      console.log(chalk.gray('Debug mode enabled'));
      console.log(chalk.gray(`Model: ${options.model}`));
      console.log(chalk.gray(`Base URL: ${options.baseUrl || 'default'}`));
      console.log(chalk.gray(`Langfuse: ${langfuseConfig.enabled ? 'enabled' : 'disabled'}`));
      if (langfuseConfig.enabled) {
        console.log(chalk.gray(`Langfuse URL: ${langfuseConfig.baseUrl}`));
      }
      console.log('');
    }

    // 生成会话ID
    const sessionId = getUUID();
    const userInfo = getUserInfo();

    // Create the agent instance
    const agent = new HaiAgent({
      model: options.model,
      baseUrl: options.baseUrl,
      apiKey: process.env.OPENAI_API_KEY,
      langfuse: {
        enabled: langfuseConfig.enabled,
        userId: userInfo.userName,
        sessionId,
        version: '0.1.0',
      },
    });

    // Determine mode based on explicit flags and input availability
    if (options.interactive) {
      // Explicitly requested interactive mode
      await runInteractiveMode(agent);
    } else if (prompt) {
      // Explicit prompt provided via command line
      try {
        for await (const chunk of agent.streamMessage(prompt)) {
          process.stdout.write(chunk);
        }
        console.log(''); // Add final newline
        await agent.flushLangfuse();
        process.exit(0);
      } catch (error) {
        console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
        await agent.flushLangfuse();
        process.exit(1);
      }
    } else if (!process.stdin.isTTY) {
      // Input from stdin (pipe)
      const stdinInput = await readStdin();
      if (stdinInput.trim()) {
        try {
          for await (const chunk of agent.streamMessage(stdinInput.trim())) {
            process.stdout.write(chunk);
          }
          console.log(''); // Add final newline
          await agent.flushLangfuse();
          process.exit(0);
        } catch (error) {
          console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
          await agent.flushLangfuse();
          process.exit(1);
        }
      } else {
        // Empty stdin, default to interactive
        await runInteractiveMode(agent);
      }
    } else {
      // No explicit prompt and TTY available, default to interactive
      await runInteractiveMode(agent);
    }
  } catch (error) {
    console.error(chalk.red('Fatal error:'), error instanceof Error ? error.message : String(error));
    if (error instanceof Error && error.stack) {
      console.error(chalk.gray(error.stack));
    }
    process.exit(1);
  }
}

// Run the CLI
main().catch((error) => {
  console.error(chalk.red('Unhandled error:'), error);
  process.exit(1);
});
