import { tool } from "@langchain/core/tools";
import { z } from "zod";
import fs from 'fs/promises';
import path from 'path';
import { homedir } from 'os';

const GEMINI_CONFIG_DIR = '.haicode';
const DEFAULT_CONTEXT_FILENAME = 'HAICODE.md';
const MEMORY_SECTION_HEADER = '## HAICODE Added Memories';

/**
 * Memory tool for langchain - saves information to long-term memory
 */
export const memoryTool = tool(
  async (params) => {
    const { fact } = params;
    
    try {
      // Get memory file path
      const memoryFilePath = path.join(homedir(), GEMINI_CONFIG_DIR, DEFAULT_CONTEXT_FILENAME);
      
      // Ensure directory exists
      const memoryDir = path.dirname(memoryFilePath);
      try {
        await fs.access(memoryDir);
      } catch {
        await fs.mkdir(memoryDir, { recursive: true });
      }
      
      // Read existing content or create new
      let currentContent = '';
      try {
        currentContent = await fs.readFile(memoryFilePath, 'utf-8');
      } catch {
        // File doesn't exist, start with empty content
        currentContent = '';
      }
      
      // Check if memory section exists
      const memorySectionIndex = currentContent.indexOf(MEMORY_SECTION_HEADER);
      
      let newContent: string;
      const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const memoryEntry = `- ${fact} (Added: ${timestamp})`;
      
      if (memorySectionIndex === -1) {
        // Memory section doesn't exist, add it
        const separator = currentContent.length > 0 ? ensureNewlineSeparation(currentContent) : '';
        newContent = currentContent + separator + MEMORY_SECTION_HEADER + '\n\n' + memoryEntry + '\n';
      } else {
        // Memory section exists, add to it
        const beforeSection = currentContent.substring(0, memorySectionIndex);
        const afterSectionStart = currentContent.substring(memorySectionIndex);
        
        // Find the end of the memory section (next ## header or end of file)
        const nextSectionMatch = afterSectionStart.substring(MEMORY_SECTION_HEADER.length).match(/\n## /);
        
        if (nextSectionMatch) {
          const nextSectionIndex = memorySectionIndex + MEMORY_SECTION_HEADER.length + nextSectionMatch.index!;
          const memorySection = currentContent.substring(memorySectionIndex, nextSectionIndex);
          const afterMemorySection = currentContent.substring(nextSectionIndex);
          
          newContent = beforeSection + memorySection + memoryEntry + '\n' + afterMemorySection;
        } else {
          // Memory section is at the end of the file
          newContent = currentContent + memoryEntry + '\n';
        }
      }
      
      // Write updated content
      await fs.writeFile(memoryFilePath, newContent, 'utf-8');
      
      const relativeMemoryPath = path.relative(process.cwd(), memoryFilePath);
      
      return `Successfully saved memory: "${fact}"\nSaved to: ${relativeMemoryPath}`;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to save memory: ${errorMessage}`);
    }
  },
  {
    name: "save_memory",
    description: "Saves a specific piece of information or fact to your long-term memory. Use this when the user explicitly asks you to remember something, or when they state a clear, concise fact that seems important to retain for future interactions.",
    schema: z.object({
      fact: z.string().describe("The specific fact or piece of information to remember. Should be a clear, self-contained statement."),
    }),
  }
);

/**
 * Ensures proper newline separation before appending content.
 */
function ensureNewlineSeparation(currentContent: string): string {
  if (currentContent.length === 0) return '';
  if (currentContent.endsWith('\n\n') || currentContent.endsWith('\r\n\r\n'))
    return '';
  if (currentContent.endsWith('\n') || currentContent.endsWith('\r\n'))
    return '\n';
  return '\n\n';
}
