import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { glob } from 'glob';
import fs from 'fs';
import path from 'path';

/**
 * Glob tool for langchain - finds files matching specific glob patterns
 */
export const globTool = tool(
  async (params) => {
    const { pattern, path: searchPath, case_sensitive, respect_git_ignore } = params;
    
    try {
      // Determine search directory
      const searchDir = searchPath ? path.resolve(searchPath) : process.cwd();
      
      // Validate search directory
      if (!fs.existsSync(searchDir)) {
        throw new Error(`Search directory does not exist: ${searchDir}`);
      }
      
      if (!fs.statSync(searchDir).isDirectory()) {
        throw new Error(`Search path is not a directory: ${searchDir}`);
      }
      
      // Create full glob pattern
      const fullPattern = path.join(searchDir, pattern);
      
      // Configure glob options
      const globOptions: any = {
        nodir: false, // Include directories in results
        absolute: true,
        stat: true, // Get file stats for sorting
      };
      
      // Handle case sensitivity
      if (case_sensitive !== undefined) {
        globOptions.nocase = !case_sensitive;
      }
      
      // Handle git ignore (simplified - in real implementation would use .gitignore parsing)
      if (respect_git_ignore !== false) {
        globOptions.ignore = [
          '**/node_modules/**',
          '**/.git/**',
          '**/dist/**',
          '**/build/**',
          '**/.DS_Store',
          '**/coverage/**'
        ];
      }
      
      // Execute glob search
      const matches = await glob(fullPattern, globOptions);
      
      if (matches.length === 0) {
        return `No files found matching pattern "${pattern}" in ${path.relative(process.cwd(), searchDir)}`;
      }
      
      // Sort files by modification time (newest first) and then alphabetically
      const filesWithStats = matches.map(filePath => {
        try {
          const stats = fs.statSync(filePath);
          return {
            path: filePath,
            mtime: stats.mtime,
            isDirectory: stats.isDirectory(),
            size: stats.size
          };
        } catch (error) {
          return {
            path: filePath,
            mtime: new Date(0),
            isDirectory: false,
            size: 0
          };
        }
      });
      
      // Sort: recent files first (within 7 days), then alphabetically
      const now = Date.now();
      const recentThreshold = 7 * 24 * 60 * 60 * 1000; // 7 days
      
      filesWithStats.sort((a, b) => {
        const aIsRecent = now - a.mtime.getTime() < recentThreshold;
        const bIsRecent = now - b.mtime.getTime() < recentThreshold;
        
        if (aIsRecent && bIsRecent) {
          return b.mtime.getTime() - a.mtime.getTime(); // Newest first
        } else if (aIsRecent) {
          return -1;
        } else if (bIsRecent) {
          return 1;
        } else {
          return a.path.localeCompare(b.path); // Alphabetical
        }
      });
      
      // Format results
      let result = `Found ${matches.length} files matching pattern "${pattern}":\n\n`;
      
      filesWithStats.forEach((file, index) => {
        const relativePath = path.relative(process.cwd(), file.path);
        const type = file.isDirectory ? '[DIR]' : '[FILE]';
        const size = file.isDirectory ? '' : ` (${file.size} bytes)`;
        const modTime = file.mtime.toISOString().split('T')[0]; // YYYY-MM-DD format
        
        result += `[${index + 1}] ${type} ${relativePath}${size} - Modified: ${modTime}\n`;
      });
      
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to find files: ${errorMessage}`);
    }
  },
  {
    name: "glob",
    description: "Efficiently finds files matching specific glob patterns (e.g., `src/**/*.ts`, `**/*.md`), returning absolute paths sorted by modification time (newest first). Ideal for quickly locating files based on their name or path structure, especially in large codebases.",
    schema: z.object({
      pattern: z.string().describe("The glob pattern to match against (e.g., '**/*.py', 'docs/*.md')."),
      path: z.string().optional().describe("Optional: The absolute path to the directory to search within. If omitted, searches the root directory."),
      case_sensitive: z.boolean().optional().describe("Optional: Whether the search should be case-sensitive (defaults to false)."),
      respect_git_ignore: z.boolean().optional().describe("Optional: Whether to respect .gitignore patterns (defaults to true)."),
    }),
  }
);
