import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { spawn } from 'child_process';
import path from 'path';

/**
 * Shell tool for langchain - executes shell commands
 */
export const shellTool = tool(
  async (params) => {
    const { command, description, directory } = params;
    
    return new Promise((resolve, reject) => {
      try {
        // Determine working directory
        const workingDir = directory ? path.resolve(directory) : process.cwd();
        
        // Execute command using bash
        const child = spawn('bash', ['-c', command], {
          cwd: workingDir,
          stdio: ['pipe', 'pipe', 'pipe'],
          detached: false,
        });
        
        let stdout = '';
        let stderr = '';
        
        // Collect stdout
        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });
        
        // Collect stderr
        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });
        
        // Handle process completion
        child.on('close', (code, signal) => {
          const relativeDir = directory ? path.relative(process.cwd(), workingDir) : '(root)';
          
          let result = `Command: ${command}\n`;
          if (description) {
            result += `Description: ${description}\n`;
          }
          result += `Directory: ${relativeDir}\n`;
          result += `Stdout: ${stdout || '(empty)'}\n`;
          result += `Stderr: ${stderr || '(empty)'}\n`;
          result += `Exit Code: ${code !== null ? code : '(none)'}\n`;
          result += `Signal: ${signal || '(none)'}`;
          
          resolve(result);
        });
        
        // Handle errors
        child.on('error', (error) => {
          reject(new Error(`Failed to execute command: ${error.message}`));
        });
        
        // Set timeout to prevent hanging
        const timeout = setTimeout(() => {
          child.kill('SIGTERM');
          reject(new Error(`Command timed out after 30 seconds: ${command}`));
        }, 30000);
        
        child.on('close', () => {
          clearTimeout(timeout);
        });
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        reject(new Error(`Failed to execute shell command: ${errorMessage}`));
      }
    });
  },
  {
    name: "run_shell_command",
    description: "Executes a given shell command as `bash -c <command>`. Returns stdout, stderr, exit code, and other execution details.",
    schema: z.object({
      command: z.string().describe("Exact bash command to execute as `bash -c <command>`"),
      description: z.string().optional().describe("Brief description of the command for the user. Be specific and concise."),
      directory: z.string().optional().describe("(OPTIONAL) Directory to run the command in, if not the project root directory. Must be relative to the project root directory and must already exist."),
    }),
  }
);
