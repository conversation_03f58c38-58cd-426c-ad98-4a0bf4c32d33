/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Re-export core types that we'll reuse
export type {
  ApprovalMode,
  AccessibilitySettings,
  BugCommandSettings,
  SummarizeToolOutputSettings,
  TelemetrySettings,
  GeminiCLIExtension,
  FileFilteringOptions,
  MCPServerConfig,
  MCPOAuthConfig,
  ConfigParameters,
} from '@google/gemini-cli-core';

export { AuthType } from '@google/gemini-cli-core';

// LangChain specific types
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { Embeddings } from '@langchain/core/embeddings';
import { Tool } from '@langchain/core/tools';

export interface LangChainConfig {
  chatModel: BaseChatModel;
  embeddings?: Embeddings;
  tools: Tool[];
  temperature?: number;
  maxTokens?: number;
  streaming?: boolean;
}

export interface LangChainContentGeneratorConfig {
  model: string;
  apiKey?: string;
  authType?: import('@google/gemini-cli-core').AuthType;
  proxy?: string;
  temperature?: number;
  maxTokens?: number;
  streaming?: boolean;
}

// Import LangChain message types
import { BaseMessage } from '@langchain/core/messages';

// Agent state for conversation management
export interface AgentState {
  messages: BaseMessage[];
  currentTool?: string;
  toolResults?: Array<{
    toolCallId: string;
    result: string;
    error?: string;
  }>;
  sessionId: string;
  userMemory?: string;
  isComplete: boolean;
  error?: string;
}

// Tool call information for agent execution
export interface ToolCall {
  id: string;
  name: string;
  args: Record<string, unknown>;
}

// Tool execution result
export interface ToolExecutionResult {
  toolCallId: string;
  result: string;
  error?: string;
}

// Extended AuthType to support OpenAI compatible providers  
export const ExtendedAuthType = {
  USE_GEMINI: 'USE_GEMINI' as const,
  USE_VERTEX_AI: 'USE_VERTEX_AI' as const,
  LOGIN_WITH_GOOGLE: 'LOGIN_WITH_GOOGLE' as const,
  CLOUD_SHELL: 'CLOUD_SHELL' as const,
  USE_OPENAI_COMPATIBLE: 'USE_OPENAI_COMPATIBLE' as const,
  USE_ANTHROPIC: 'USE_ANTHROPIC' as const,
} as const;

export type ExtendedAuthType = typeof ExtendedAuthType[keyof typeof ExtendedAuthType];

// Model configuration for different providers
export interface ModelConfig {
  provider: 'gemini' | 'vertex-ai' | 'openai' | 'anthropic';
  modelName: string;
  apiKey?: string;
  baseURL?: string;
  projectId?: string;
  location?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface LangChainToolWrapper {
  name: string;
  description: string;
  schema: Record<string, unknown>;
  func: (input: Record<string, unknown>) => Promise<string>;
}

// Optional LangGraph MongoDB checkpointer configuration
export interface LangGraphMongoConfig {
  uri: string;
  dbName?: string;
  checkpointCollectionName?: string;
  checkpointWritesCollectionName?: string;
}

// Optional Langfuse configuration for LangChain callbacks
export interface LangfuseConfig {
  enabled?: boolean;
  publicKey?: string;
  secretKey?: string;
  baseUrl?: string;
  userId?: string;
  sessionId?: string;
  release?: string;
  version?: string;
  updateRoot?: boolean;
}