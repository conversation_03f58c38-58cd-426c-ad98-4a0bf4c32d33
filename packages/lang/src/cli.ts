#!/usr/bin/env node
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * hai-code - A simple CLI entry point that uses the lang package agent logic
 * Supports reading LLM configuration from environment variables like OPENAI_BASE_URL, OPENAI_API_KEY, etc.
 */

// CRITICAL: Set telemetry environment variables BEFORE importing any LangChain modules
// process.env.LANGCHAIN_TRACING_V2 = 'false';
// process.env.LANGCHAIN_TRACING = 'false';
// process.env.LANGCHAIN_ENDPOINT = '';
// process.env.LANGCHAIN_API_KEY = '';
// process.env.LANGSMITH_API_KEY = '';
// process.env.LANGSMITH_TRACING = 'false';
// process.env.OPENAI_ORGANIZATION = '';
// process.env.LANGCHAIN_CALLBACKS_BACKGROUND = 'false';
// process.env.LANGCHAIN_VERBOSE = 'false';
// process.env.LANGCHAIN_DEBUG = 'false';
// process.env.LANGCHAIN_LOG_LEVEL = 'ERROR';
// process.env.LANGCHAIN_DISABLE_TELEMETRY = 'true';
// 打印LLM输入
process.env.LOGGING_LLM_INPUT = 'true';

import './utils-cli/overwrite.js';
import chalk from 'chalk';
import log from './utils/logger.js';
import { createHaicodeAgent } from './index.js';
import { readStdin } from './utils/readStdin.js';
import { getUUID } from './utils/uuid.js';
import { parseCliArguments } from './utils-cli/parseArg.js';
import { determineAuthType } from './utils-cli/authType.js';
import { DEFAULT_HAI_CODE_MODEL } from './config/models.js';
import { getUserInfo } from './utils/user.js';
import { runInteractiveMode } from './utils-cli/interactive.js';
import { printDebugInfo } from './utils-cli/print.js';

function printHelp() {
  log.info(chalk.cyan(`
hai-code - AI Coding Assistant CLI

Usage:
  hai-code [options] [prompt]
  echo "your question" | hai-code

Options:
  -m, --model <model>     Model to use (default: ht::saas-deepseek-v3)
  -p, --prompt <prompt>   Prompt to process (non-interactive mode)
  -b, --base-url <url>    Base URL for LLM API (can also use OPENAI_BASE_URL env)
  -i, --interactive       Start in interactive mode (default if no prompt)
  -d, --debug             Enable debug mode
  -g, --use-google        Enable google tools (web_search, web_fetch.)
  -h, --help              Show this help message
  -v, --version           Show version

Environment Variables:
  OPENAI_API_KEY          OpenAI API key
  OPENAI_BASE_URL         OpenAI-compatible API base URL
  ANTHROPIC_API_KEY       Anthropic API key  
  GEMINI_API_KEY          Google Gemini API key
  GOOGLE_CLOUD_PROJECT    Google Cloud project for Vertex AI

Examples:
  hai-code "法国首都是什么？"
  hai-code -m ht::saas-deepseek-v3 "解释量子计算"
  echo "修复这个代码" | hai-code
  OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama hai-code -m ht::saas-deepseek-v3 "你好"
`));
}

function printVersion() {
  log.info(`hai-code version 0.1.7`);
}

async function main() {
  try {
    const { options, prompt } = await parseCliArguments();

    // Handle EPIPE errors when piping output to commands that close early (e.g., head)
    process.stdout.on('error', (err: NodeJS.ErrnoException) => {
      if (err && err.code === 'EPIPE') {
        process.exit(0);
      }
    });

    if (options.help) {
      printHelp();
      process.exit(0);
    }

    if (options.version) {
      printVersion();
      process.exit(0);
    }

    printDebugInfo({
      model: options.model || DEFAULT_HAI_CODE_MODEL,
      debug: options.debug || false,
    });

    // Create the CLI instance
    const agent = await createHaicodeAgent({
      sessionId: getUUID(),
      model: options.model || DEFAULT_HAI_CODE_MODEL,
      targetDir: process.cwd(),
      debugMode: options.debug || false,
      useGoogle: options.useGoogle || false,
      question: '',
      fullContext: false,
      userMemory: '',
      userId: getUserInfo().userName,
      geminiMdFileCount: 50,
      cwd: process.cwd(),
      baseURL: options.baseUrl,
      authType: determineAuthType(),
      telemetry: {
        enabled: false,
      },
      mcpServers: {
        "browser-tools": {
          "command": "uvx",
          "args": [
            "mcp-server-time"
          ]
        }
      },
      checkpointer: true,
    });

    // Determine mode based on explicit flags and input availability
    if (options.interactive) {
      // Explicitly requested interactive mode
      await runInteractiveMode(agent);
    } else if (prompt) {
      // Explicit prompt provided via command line
      try {
        const stream = agent.streamMessage(prompt);
        for await (const chunk of stream) {
          process.stdout.write(chunk);
        }
        log.info(''); // Add final newline
        process.exit(0);
      } catch (error) {
        log.error('Error:', error instanceof Error ? error.message : String(error));
        process.exit(1);
      }
    } else if (!process.stdin.isTTY) {
      // Input from stdin (pipe)
      const stdinInput = await readStdin();
      if (stdinInput.trim()) {
        try {
          const stream = agent.streamMessage(stdinInput.trim());
          for await (const chunk of stream) {
            process.stdout.write(chunk);
          }
          log.info(''); // Add final newline
          process.exit(0);
        } catch (error) {
          log.error('Error:', error instanceof Error ? error.message : String(error));
          process.exit(1);
        }
      } else {
        // Empty stdin, default to interactive
        await runInteractiveMode(agent);
      }
    } else {
      // No explicit prompt and TTY available, default to interactive
      await runInteractiveMode(agent);
    }

  } catch (error) {
    log.error('Fatal error:', error instanceof Error ? error.message : String(error));
    if (error instanceof Error && error.stack) {
      log.error('Stack:', error.stack);
    }
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  // Check if it's a network timeout error that we can ignore
  if (reason && typeof reason === 'object' && 'code' in reason) {
    const error = reason as { code?: string; message?: string };
    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNREFUSED') {
      log.debug('Network timeout ignored (likely telemetry):', error.message);
      return; // Don't exit for network timeouts
    }
  }
  
  // Check for LangChain telemetry errors
  if (reason && typeof reason === 'object' && 'message' in reason) {
    const errorMessage = String(reason.message).toLowerCase();
    if (errorMessage.includes('flushing log events') || 
        errorMessage.includes('216.239.') || 
        errorMessage.includes('langsmith') ||
        errorMessage.includes('tracing')) {
      log.debug('LangChain telemetry error ignored:', reason.message);
      return; // Don't exit for telemetry errors
    }
  }
  
  log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  // Check if it's a network timeout error that we can ignore
  const nodeError = error as NodeJS.ErrnoException;
  if (nodeError.code === 'ETIMEDOUT' || nodeError.code === 'ECONNREFUSED') {
    log.debug('Network timeout ignored (likely telemetry):', error.message);
    return; // Don't exit for network timeouts
  }
  
  // Check for LangChain telemetry errors
  const errorMessage = error.message.toLowerCase();
  if (errorMessage.includes('flushing log events') || 
      errorMessage.includes('216.239.') || 
      errorMessage.includes('langsmith') ||
      errorMessage.includes('tracing')) {
    log.debug('LangChain telemetry error ignored:', error.message);
    return; // Don't exit for telemetry errors
  }
  
  log.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the CLI
main();
