/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'node:path';
import os from 'os';
// import * as crypto from 'crypto';

export const HAICODE_DIR = '.haicode';
export const HAICODE_ACCOUNTS_FILENAME = 'haicode_accounts.json';
const TMP_DIR_NAME = 'tmp';
const COMMANDS_DIR_NAME = 'commands';

export function getHomeDir(): string {
  return os.homedir();
}

export function getHaiCodeDir(): string {
  return path.join(getHomeDir(), HAICODE_DIR);
}

/**
 * Replaces the home directory with a tilde.
 * @param path - The path to tildeify.
 * @returns The tildeified path.
 */
export function tildeifyPath(path: string): string {
  const homeDir = os.homedir();
  if (path.startsWith(homeDir)) {
    return path.replace(homeDir, '~');
  }
  return path;
}

/**
 * Generates a unique hash for a project based on its root path.
 * @param projectRoot The absolute path to the project's root directory.
 * @returns A SHA256 hash of the project root path.
 */
export function getProjectHash(projectRoot: string): string {
  // console.log('projectRoot', projectRoot);
  // 路径更加语义化
  return encodeURIComponent(projectRoot.replace(/\//g, '__'));
  // return crypto.createHash('sha256').update(projectRoot).digest('hex');
}

/**
 * Generates a unique temporary directory path for a project using .haicode.
 * @param projectRoot The absolute path to the project's root directory.
 * @returns The path to the project's temporary directory.
 */
export function getProjectTempDir(projectRoot: string): string {
  const hash = getProjectHash(projectRoot);
  return path.join(os.homedir(), HAICODE_DIR, TMP_DIR_NAME, hash);
}

/**
 * Returns the absolute path to the user-level commands directory.
 * @returns The path to the user's commands directory.
 */
export function getUserCommandsDir(): string {
  return path.join(os.homedir(), HAICODE_DIR, COMMANDS_DIR_NAME);
}

/**
 * Returns the path to the .haicode directory.
 * @param targetDir The target directory (defaults to user home).
 * @returns The path to the .haicode directory.
 */
export function getHaicodeDir(targetDir?: string): string {
  return path.join(targetDir || os.homedir(), HAICODE_DIR);
}