export const isHtModel = (model: string) => model.startsWith('ht::')

export const easyGetNumTokens = (text: string) => Math.ceil(text.length / 4);

export const easyGetNumTokensFromMessages = (messages: unknown[]) => {
    const totalChars = messages.reduce((sum: number, msg: unknown) => {
        const msgObj = msg as { content?: unknown };
        const content = typeof msgObj?.content === 'string' ? msgObj.content : JSON.stringify(msgObj?.content || '');
        return sum + content.length;
    }, 0);
    const estimatedTokens = Math.ceil(totalChars / 4);
    return {
        totalCount: estimatedTokens,
        countPerMessage: messages.map(() => Math.ceil(estimatedTokens / messages.length))
    };
}