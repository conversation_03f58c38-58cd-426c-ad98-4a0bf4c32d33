/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { logger, setDebugMode, getDebugMode } from './logger.js';

describe('Logger', () => {
  let consoleDebugSpy: ReturnType<typeof vi.spyOn>;
  let consoleLogSpy: ReturnType<typeof vi.spyOn>;
  let consoleErrorSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    // Reset debug mode to false before each test
    setDebugMode(false);
    
    // Spy on console methods
    consoleDebugSpy = vi.spyOn(console, 'debug').mockImplementation(() => {});
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console methods
    consoleDebugSpy.mockRestore();
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  describe('setDebugMode and getDebugMode', () => {
    it('should set and get debug mode correctly', () => {
      expect(getDebugMode()).toBe(false);
      
      setDebugMode(true);
      expect(getDebugMode()).toBe(true);
      
      setDebugMode(false);
      expect(getDebugMode()).toBe(false);
    });
  });

  describe('debug logging', () => {
    it('should not log debug messages when debug mode is disabled', () => {
      setDebugMode(false);
      
      logger.debug('This should not be logged');
      
      expect(consoleDebugSpy).not.toHaveBeenCalled();
    });

    it('should log debug messages when debug mode is enabled', () => {
      setDebugMode(true);
      
      logger.debug('This should be logged');
      
      expect(consoleDebugSpy).toHaveBeenCalledWith(
        expect.stringContaining('This should be logged')
      );
    });

    it('should log debug messages with additional arguments when debug mode is enabled', () => {
      setDebugMode(true);
      
      const testObject = { key: 'value' };
      logger.debug('Debug message with args', testObject, 123);
      
      expect(consoleDebugSpy).toHaveBeenCalledWith(
        expect.stringContaining('Debug message with args'),
        testObject,
        123
      );
    });
  });

  describe('other log levels', () => {
    it('should always log info messages regardless of debug mode', () => {
      setDebugMode(false);
      logger.info('Info message');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Info message')
      );

      consoleLogSpy.mockClear();
      
      setDebugMode(true);
      logger.info('Info message 2');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Info message 2')
      );
    });

    it('should always log success messages regardless of debug mode', () => {
      setDebugMode(false);
      logger.success('Success message');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Success message')
      );

      consoleLogSpy.mockClear();
      
      setDebugMode(true);
      logger.success('Success message 2');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Success message 2')
      );
    });

    it('should always log warning messages regardless of debug mode', () => {
      setDebugMode(false);
      logger.warning('Warning message');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning message')
      );

      consoleLogSpy.mockClear();
      
      setDebugMode(true);
      logger.warning('Warning message 2');
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning message 2')
      );
    });

    it('should always log error messages regardless of debug mode', () => {
      setDebugMode(false);
      logger.error('Error message');
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error message')
      );

      consoleErrorSpy.mockClear();
      
      setDebugMode(true);
      logger.error('Error message 2');
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error message 2')
      );
    });
  });
});