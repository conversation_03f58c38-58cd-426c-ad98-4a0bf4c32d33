/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  AIMessage,
  AIMessageChunk,
  HumanMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { ToolResult } from '@google/gemini-cli-core';
import logger from './logger.js';

/**
 * 统一的工具结果处理工具类
 * 用于消除代码中的冗余逻辑，提供一致的工具结果处理、消息清理和错误处理
 */
export class ToolResultProcessor {
  private static readonly DEFAULT_EMPTY_RESULT = 'No output';
  private static readonly DEFAULT_BINARY_MESSAGE = 'Binary content was processed.';

  /**
   * 将任意内容转换为安全的字符串
   * 统一处理各种类型的内容转换
   */
  static toSafeString(content: unknown): string {
    if (typeof content === 'string') {
      return content;
    }
    
    if (!content) {
      return '';
    }
    
    if (Array.isArray(content)) {
      const parts = content
        .map((part) => this.extractTextFromPart(part))
        .filter(Boolean);
      return parts.join('\n');
    }
    
    if (typeof content === 'object' && 'text' in (content as Record<string, unknown>)) {
      const txt = (content as { text?: string }).text;
      return typeof txt === 'string' ? txt : '';
    }
    
    try {
      return JSON.stringify(content);
    } catch (_e) {
      return String(content);
    }
  }

  /**
   * 从部分内容中提取文本
   */
  private static extractTextFromPart(part: unknown): string {
    if (typeof part === 'string') {
      return part;
    }
    
    if (part && typeof part === 'object' && 'text' in (part as Record<string, unknown>)) {
      const txt = (part as { text?: string }).text;
      return typeof txt === 'string' ? txt : '';
    }
    
    return '';
  }

  /**
   * 检查字符串是否为有效内容（非空且非纯空白）
   */
  static isValidContent(content: string): boolean {
    return content.trim().length > 0;
  }

  /**
   * 格式化工具结果为字符串，支持空值安全检查
   * 统一处理 ToolResult 类型的转换逻辑
   */
  static formatToolResult(result: ToolResult): string {
    // 优先使用 llmContent（LLM 应该看到的内容）
    if (Array.isArray(result.llmContent)) {
      const joined = result.llmContent
        .map(part => {
          if (typeof part === 'string') return part;
          if (typeof part === 'object' && part !== null && 'text' in part) {
            return (part as { text?: string }).text || '';
          }
          return JSON.stringify(part);
        })
        .join('\n');
      
      return this.isValidContent(joined) ? joined : this.DEFAULT_EMPTY_RESULT;
    }
    
    if (typeof result.llmContent === 'string') {
      return this.isValidContent(result.llmContent) ? result.llmContent : this.DEFAULT_EMPTY_RESULT;
    }
    
    if (result.llmContent && typeof result.llmContent === 'object') {
      // 处理二进制内容（图片等）
      if ('inlineData' in result.llmContent) {
        const mimeType = (result.llmContent as any).inlineData?.mimeType || 'unknown';
        return `${this.DEFAULT_BINARY_MESSAGE} Type: ${mimeType}`;
      }
      
      const jsonString = JSON.stringify(result.llmContent);
      return jsonString && jsonString !== '{}' ? jsonString : this.DEFAULT_EMPTY_RESULT;
    }

    // 回退到 returnDisplay
    return this.formatReturnDisplay(result.returnDisplay);
  }

  /**
   * 格式化返回显示内容
   */
  private static formatReturnDisplay(returnDisplay: unknown): string {
    if (typeof returnDisplay === 'string') {
      return this.isValidContent(returnDisplay) ? returnDisplay : this.DEFAULT_EMPTY_RESULT;
    }
    
    if (returnDisplay && typeof returnDisplay === 'object') {
      // 处理 FileDiff 情况
      if ('fileDiff' in returnDisplay && 'fileName' in returnDisplay) {
        const diff = returnDisplay as { fileName: string; fileDiff: string };
        return `File modified: ${diff.fileName}\n\n${diff.fileDiff}`;
      }
      
      const jsonString = JSON.stringify(returnDisplay);
      return jsonString && jsonString !== '{}' ? jsonString : this.DEFAULT_EMPTY_RESULT;
    }

    return this.DEFAULT_EMPTY_RESULT;
  }

  /**
   * 清理和标准化消息内容
   * 确保所有消息都有有效的字符串内容并保留工具元数据
   */
  static sanitizeMessages(messages: BaseMessage[]): BaseMessage[] {
    return messages
      .filter((m) => !!m)
      .map((message) => this.sanitizeMessage(message));
  }

  /**
   * 清理单个消息
   */
  private static sanitizeMessage(message: BaseMessage): BaseMessage {
    if (message instanceof ToolMessage) {
      const safeContent = this.toSafeString(message.content);
      return new ToolMessage({
        content: safeContent || this.DEFAULT_EMPTY_RESULT,
        tool_call_id: message.tool_call_id,
        additional_kwargs: message.additional_kwargs,
      });
    }
    
    if (message instanceof AIMessage) {
      const safeContent = this.toSafeString(message.content);
      const aiMessage = new AIMessage({ 
        content: safeContent, 
        additional_kwargs: message.additional_kwargs 
      });
      
      // 保留工具调用信息
      if (message.tool_calls && message.tool_calls.length > 0) {
        (aiMessage as AIMessage).tool_calls = message.tool_calls;
      }
      
      return aiMessage;
    }
    
    if (message instanceof HumanMessage) {
      const safeContent = this.toSafeString(message.content);
      return new HumanMessage({ 
        content: safeContent, 
        additional_kwargs: message.additional_kwargs 
      });
    }
    
    if (message instanceof SystemMessage) {
      const safeContent = this.toSafeString(message.content);
      // 确保SystemMessage有有效内容，避免空消息
      return new SystemMessage(this.isValidContent(safeContent) ? safeContent : this.DEFAULT_EMPTY_RESULT);
    }
    
    // 处理未知消息类型
    const unknownMessage = message as unknown as { content?: unknown };
    const unknownContent = this.toSafeString(unknownMessage?.content);
    // 确保未知类型消息也有有效内容
    return new SystemMessage(this.isValidContent(unknownContent) ? unknownContent : this.DEFAULT_EMPTY_RESULT);
  }

  /**
   * 为不支持工具消息的服务器剥离工具语义
   * 提供fallback机制
   */
  static stripToolMessagesForFallback(messages: BaseMessage[]): BaseMessage[] {
    return messages.map((msg) => this.stripToolSemantics(msg));
  }

  /**
   * 剥离单个消息的工具语义
   */
  private static stripToolSemantics(msg: BaseMessage): BaseMessage {
    if (msg instanceof ToolMessage) {
      const toolContent = this.toSafeString(msg.content);
      // 确保转换后的SystemMessage有有效内容，避免空消息
      const safeContent = this.isValidContent(toolContent) ? toolContent : this.DEFAULT_EMPTY_RESULT;
      return new SystemMessage(`Previous tool execution result: ${safeContent}`);
    }
    
    if (msg instanceof AIMessage) {
      // 移除工具调用，只保留内容
      return new AIMessage({ 
        content: this.toSafeString(msg.content), 
        additional_kwargs: msg.additional_kwargs 
      });
    }
    
    if (msg instanceof HumanMessage) {
      return new HumanMessage(this.toSafeString(msg.content));
    }
    
    if (msg instanceof SystemMessage) {
      return new SystemMessage(this.toSafeString(msg.content));
    }
    
    const unknownMessage = msg as unknown as { content?: unknown };
    return new SystemMessage(this.toSafeString(unknownMessage?.content));
  }

  /**
   * 处理MCP工具结果
   * 专门用于MCP工具的结果处理
   */
  static formatMcpResult(returnDisplay: unknown): string {
    if (typeof returnDisplay === 'string') {
      return this.isValidContent(returnDisplay) ? returnDisplay : this.DEFAULT_EMPTY_RESULT;
    }
    
    if (returnDisplay && typeof returnDisplay === 'object') {
      const jsonString = JSON.stringify(returnDisplay);
      return jsonString && jsonString !== '{}' ? jsonString : this.DEFAULT_EMPTY_RESULT;
    }
    
    return returnDisplay ? String(returnDisplay) : this.DEFAULT_EMPTY_RESULT;
  }

  /**
   * 统一的错误处理方法
   * 提供一致的错误消息格式
   */
  static formatError(toolName: string, error: unknown): string {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return `Tool execution was cancelled: ${toolName}`;
      } else if (error.message.includes('timeout')) {
        return `Tool execution timed out: ${toolName}`;
      } else if (error.message.includes('permission')) {
        return `Permission denied for tool: ${toolName}`;
      }
    }
    
    return `Error executing ${toolName}: ${errorMessage}`;
  }

  /**
   * 记录工具结果的调试信息
   */
  static logToolResult(toolName: string, result: string, debugMode: boolean): void {
    if (debugMode) {
      const truncatedResult = result.length > 500 
        ? result.substring(0, 500) + '...(truncated)'
        : result;
      
      logger.debug(`[ToolResultProcessor] Tool ${toolName} result: ${truncatedResult}`);
    }
  }
}
