/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import logger from '../utils/logger.js';

// Intercept and block telemetry network requests
const originalFetch = globalThis.fetch;
if (originalFetch) {
  globalThis.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
    
    // Block known telemetry endpoints
    if (url.includes('googleapis.com') ||
        url.includes('google.com/analytics')) {
      logger.debug('Blocked telemetry request to:', url);
      // Return a fake successful response
      return new Response('{"status": "disabled"}', { 
        status: 200, 
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return originalFetch(input, init);
  };
}

// Override console.error to filter out telemetry errors
const originalConsoleError = console.error;
console.error = function(...args: unknown[]) {
  const message = args.join(' ');
  
  // Filter out telemetry-related errors
  if (message.includes('Error flushing log events')) {
    // logger.debug('Telemetry error suppressed:', message);
    return;
  }
  
  return originalConsoleError.apply(console, args);
};

const originalConsoleLog = console.log;
console.log = function(...args: unknown[]) {
  const message = args.join(' ');
  // TODO: disable telemetry log
  if (message.includes('Clearcut POST request error')) {
    return;
  }
  return originalConsoleLog.apply(console, args);
};

// Note: The above code is commented out because we now use the logger module directly
// instead of overriding console.error. This ensures consistent logging throughout the application.


// Temporarily suppress Zod schema warnings for OpenAI API compatibility
const originalWarn = console.warn;
console.warn = (message: string, ...args: unknown[]) => {
  if (typeof message === 'string' &&
      message.includes('Zod field') &&
      message.includes('uses `.optional()` without `.nullable()`')) {
    return; // Suppress Zod schema warnings
  }
  originalWarn(message, ...args);
};
