import chalk from "chalk";
import log from "../utils/logger.js";
import { determineAuthType } from "./authType.js";
import { DEFAULT_HAI_CODE_MODEL } from "../config/models.js";

export function printDebugInfo(options: { model: string; debug: boolean }) {
  if (options.debug) {
    log.info('Debug mode enabled');
    log.info('Environment variables:');
    log.info('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? chalk.green('√') : chalk.red('×'));
    log.info('  OPENAI_BASE_URL:', process.env.OPENAI_BASE_URL ? chalk.green(process.env.OPENAI_BASE_URL) : chalk.red('×'));
    log.info('  ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? chalk.green('√') : chalk.red('×'));
    log.info('  GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? chalk.green('√') : chalk.red('×'));
    log.info('  GOOGLE_CLOUD_PROJECT:', process.env.GOOGLE_CLOUD_PROJECT ? chalk.green(process.env.GOOGLE_CLOUD_PROJECT) : chalk.red('×'));
  }
  log.info('Auth type:', chalk.green(determineAuthType()));
  log.info('Model:', chalk.green(options.model || DEFAULT_HAI_CODE_MODEL));
  log.info('');
}
