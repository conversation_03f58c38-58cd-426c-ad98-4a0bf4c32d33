import { getUUID } from '../utils/uuid.js';
import log from '../utils/logger.js';

export async function runInteractiveMode(agent: {
  streamMessage: (userMessage: string, sessionId?: string, userMemory?: string) => AsyncGenerator<string>;
}) {
  log.success('🤖 hai-code - Interactive mode');
  log.info('Type "exit" or "quit" to exit, Ctrl+C to quit immediately');
  // log.info('');

  // Create a persistent session for the entire interactive session
  const interactiveSessionId = getUUID();
  log.debug(`\n[CLI] Created interactive session: ${interactiveSessionId}`);

  // State for handling permission requests
  let waitingForPermission = false;

  // Use readline for interactive input
  const readline = await import('node:readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '> ',
  });

  rl.prompt();

  rl.on('line', async (input) => {
    const trimmed = input.trim();
    log.debug(`\n[CLI] Received input: "${trimmed}"`);

    if (trimmed === 'exit' || trimmed === 'quit') {
      rl.close();
      return;
    }

    if (trimmed === '') {
      rl.prompt();
      return;
    }

    try {
      // Check if this looks like a permission response
      if (waitingForPermission && isPermissionResponse(trimmed)) {
        log.info(`\nPermission response received: ${trimmed}`);
        // Handle permission response
        const permissionGranted = parsePermissionResponse(trimmed);
        if (permissionGranted) {
          log.success('✅ Permission granted, proceeding...');
        } else {
          log.warning('❌ Permission denied, operation cancelled.');
        }
        waitingForPermission = false;
        rl.prompt();
        return;
      }

      // log.debug(`\n[CLI] Starting to stream message for session: ${interactiveSessionId}`);
      
      // Use the same session ID for all messages in this interactive session
      const stream = agent.streamMessage(trimmed, interactiveSessionId);

      let responseContent = '';
      let chunkCount = 0;
      
      try {
        for await (const chunk of stream) {
          chunkCount++;
          responseContent += chunk;
          process.stdout.write(chunk);
          
          // Add periodic debug info for long responses
          if (chunkCount % 50 === 0) {
            log.debug(`\n[CLI] Processed ${chunkCount} chunks, ${responseContent.length} chars`);
          }
        }
        
        log.debug(`\n[CLI] Stream completed: ${chunkCount} chunks, ${responseContent.length} chars`);
      } catch (streamError) {
        log.error('\n[CLI] Stream error:', streamError);
        throw streamError;
      }

      // Check if the response contains a permission request
      if (containsPermissionRequest(responseContent)) {
        waitingForPermission = true;
        log.info('\n💡 Tip: Respond with "y/yes" to approve, "n/no" to deny, or "a/always" to always approve this type of operation.');
      }

      log.info(''); // New line after response
    } catch (error) {
      log.error('\nError:', error instanceof Error ? error.message : String(error));
      log.error('\nError stack:', error instanceof Error ? error.stack : 'No stack trace');
      waitingForPermission = false;
    }

    log.debug('\n[CLI] Prompting for next input');
    rl.prompt();
  });

  rl.on('close', () => {
    log.success('\nGoodbye!');
    process.exit(0);
  });
}

/**
 * Check if input looks like a permission response
 */
function isPermissionResponse(input: string): boolean {
  const normalized = input.toLowerCase();
  const permissionResponses = [
    'y', 'yes', 'n', 'no', 'a', 'always', 'never', 
    'approve', 'deny', 'allow', 'reject', 'proceed', 'cancel'
  ];
  return permissionResponses.includes(normalized);
}

/**
 * Parse permission response to boolean
 */
function parsePermissionResponse(input: string): boolean {
  const normalized = input.toLowerCase();
  const positiveResponses = ['y', 'yes', 'a', 'always', 'approve', 'allow', 'proceed'];
  return positiveResponses.includes(normalized);
}

/**
 * Check if response contains a permission request
 */
function containsPermissionRequest(content: string): boolean {
  const permissionIndicators = [
    'Do you want to proceed',
    'Would you like to continue',
    'Confirm this action',
    'Are you sure',
    'Permission required',
    'Approve this operation',
    'Execute this command',
    'Run this tool',
    'y/n',
    '(y/N)',
    '(Y/n)',
    'yes/no'
  ];
  
  return permissionIndicators.some(indicator => 
    content.toLowerCase().includes(indicator.toLowerCase())
  );
}
