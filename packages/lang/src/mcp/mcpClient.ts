/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Tool } from '@langchain/core/tools';
import { z } from 'zod';

// Import core MCP functionality
import {
  discoverMcpTools,
  DiscoveredMCPTool,
  ToolRegistry as CoreToolRegistry,
} from '@google/gemini-cli-core';

import logger from '../utils/logger.js';
import { ToolResultProcessor } from '../utils/toolResultProcessor.js';

import type { LangChainConfig } from '../config/config.js';

/**
 * Wrapper that adapts MCP tools from core package to LangChain tools
 */
export class MCPToolAdapter extends Tool {
  name: string;
  description: string;

  constructor(
    private mcpTool: DiscoveredMCPTool,
    private config: LangChainConfig
  ) {
    super();
    this.name = mcpTool.name;
    this.description = this.generateEnhancedDescription();

    // Set schema using LangChain compatible format
    this.schema = this.createLangChainSchema();
  }

  /**
   * Generate enhanced description that includes parameter information
   */
  private generateEnhancedDescription(): string {
    let description = this.mcpTool.description;

    try {
      const paramSchema = this.mcpTool.parameterSchemaJson as Record<string, unknown>;
      if (paramSchema && paramSchema.type === 'object' && paramSchema.properties) {
        description += '\n\n**Parameters:**';
        const properties = paramSchema.properties as Record<string, Record<string, unknown>>;
        const required = (paramSchema.required as string[]) || [];

        for (const [paramName, paramDef] of Object.entries(properties)) {
          const isRequired = required.includes(paramName) ? ' (required)' : ' (optional)';
          const paramDesc = (paramDef.description as string) || 'No description';
          const paramType = (paramDef.type as string) || 'string';
          description += `\n- **${paramName}** (${paramType})${isRequired}: ${paramDesc}`;
        }
      }
    } catch (error) {
      logger.debug(`[MCPToolAdapter] Failed to parse parameter schema for ${this.name}:`, error);
    }

    return description;
  }

  /**
   * Create LangChain compatible schema
   * For now, use a simple schema that accepts all parameters as a JSON object
   */
  private createLangChainSchema() {
    // Use the same pattern as CoreToolWrapper for compatibility
    return z.object({
      input: z.string().optional().describe('Tool input parameters as JSON string')
    }).transform((val) => val.input || '');
  }

  /**
     * Execute the core tool and return the result as a string
     * @abstract
     */
    async _call(input: string): Promise<string> {
        return input;
    }

    async invoke(input: Record<string, unknown>): Promise<string> {
      try {
        const args = input.args;

        if (this.config.getDebugMode()) {
          logger.debug(`[MCPToolAdapter] Executing MCP tool ${this.name} with args:`, args);
        }

        // Execute the MCP tool through core implementation
        const result = await this.mcpTool.execute(args as any);

        // Format the result for LangChain consumption using unified processor
        return ToolResultProcessor.formatMcpResult(result.returnDisplay);
      } catch (error) {
        logger.error(`[MCPToolAdapter] Error executing MCP tool ${this.name}:`, error);
        return ToolResultProcessor.formatError(`MCP tool ${this.name}`, error);
      }
    }
  }

/**
 * LangChain-compatible MCP client that discovers and adapts MCP tools
 */
export class LangChainMCPClient {
  private discoveredTools: Map<string, MCPToolAdapter> = new Map();
  
  constructor(private config: LangChainConfig) {}

  /**
   * Discover tools from all configured MCP servers
   */
  async discoverMCPTools(): Promise<Tool[]> {
    try {
      const mcpServers = this.config.getMcpServers() ?? {};
      const mcpServerCommand = this.config.getMcpServerCommand();
      
      if (Object.keys(mcpServers).length === 0 && !mcpServerCommand) {
        logger.debug('[LangChainMCPClient] No MCP servers configured');
        return [];
      }

      // Create a temporary core tool registry to collect discovered tools
      // Create a minimal config object that satisfies core Config interface
      const tempCoreConfig = {
        sessionId: 'temp-mcp-discovery',
        targetDir: this.config.getTargetDir(),
        model: this.config.getModel(),
        cwd: this.config.getWorkingDir(),
        debugMode: this.config.getDebugMode(),
        approvalMode: this.config.getApprovalMode(),
        getUserMemory: () => this.config.getUserMemory(),
        setUserMemory: (memory: string) => this.config.setUserMemory(memory),
        getFileFilteringOptions: () => this.config.getFileFilteringOptions(),
        getMcpServers: () => this.config.getMcpServers(),
        getMcpServerCommand: () => this.config.getMcpServerCommand(),
        getUsageStatisticsEnabled: () => this.config.getUsageStatisticsEnabled(),
      };
      const tempCoreRegistry = new CoreToolRegistry(tempCoreConfig as unknown as import('@google/gemini-cli-core').Config);
      
      // Use core package's MCP discovery logic
      await discoverMcpTools(
        mcpServers,
        mcpServerCommand,
        tempCoreRegistry,
        this.config.getDebugMode()
      );

      // Convert discovered MCP tools to LangChain tools
      const langChainTools: Tool[] = [];
      const coreTools = await tempCoreRegistry.getAllTools();
      
      let discoveredCount = 0;
      const failedTools: string[] = [];
      
      for (const coreTool of coreTools) {
        if (coreTool instanceof DiscoveredMCPTool) {
          try {
            const langChainTool = new MCPToolAdapter(coreTool, this.config);
            this.discoveredTools.set(coreTool.name, langChainTool);
            langChainTools.push(langChainTool);
            discoveredCount++;
            
            logger.debug(`[LangChainMCPClient] Discovered MCP tool: ${coreTool.name}`);
          } catch (error) {
            logger.warning(`[LangChainMCPClient] Failed to adapt MCP tool ${coreTool.name}:`, error);
            failedTools.push(coreTool.name);
          }
        }
      }

      logger.success(`[LangChainMCPClient] Successfully discovered ${discoveredCount} MCP tools`);
      if (failedTools.length > 0) {
        logger.warning(`[LangChainMCPClient] Failed to adapt ${failedTools.length} MCP tools:`, failedTools);
      }
      return langChainTools;
      
    } catch (error) {
      logger.error('[LangChainMCPClient] Failed to discover MCP tools:', error);
      
      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('connection')) {
          throw new Error('Failed to connect to MCP servers. Please check your MCP configuration.');
        } else if (error.message.includes('timeout')) {
          throw new Error('MCP server discovery timed out. Please try again.');
        } else if (error.message.includes('permission')) {
          throw new Error('Permission denied accessing MCP servers. Please check your permissions.');
        } else {
          throw new Error(`MCP tool discovery failed: ${error.message}`);
        }
      } else {
        throw new Error(`MCP tool discovery failed: ${String(error)}`);
      }
    }
  }

  /**
   * Get a specific discovered MCP tool by name
   */
  getMCPTool(toolName: string): MCPToolAdapter | undefined {
    return this.discoveredTools.get(toolName);
  }

  /**
   * Get all discovered MCP tools
   */
  getAllMCPTools(): MCPToolAdapter[] {
    return Array.from(this.discoveredTools.values());
  }

  /**
   * Check if MCP is configured
   */
  isMCPConfigured(): boolean {
    const mcpServers = this.config.getMcpServers() ?? {};
    const mcpServerCommand = this.config.getMcpServerCommand();
    return Object.keys(mcpServers).length > 0 || !!mcpServerCommand;
  }

  /**
   * Clear all discovered tools (useful for re-discovery)
   */
  clearDiscoveredTools(): void {
    this.discoveredTools.clear();
  }
}