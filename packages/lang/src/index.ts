/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Main exports for the LangChain-based Gemini CLI implementation

import { MongoClient } from 'mongodb';
import { MongoDBSaver } from '@langchain/langgraph-checkpoint-mongodb';
import { <PERSON>back<PERSON>and<PERSON> } from 'langfuse-langchain';
import { BaseMessage } from '@langchain/core/messages';

// Configuration
export { LangChainConfig } from './config/config.js';
export type { 
  LangChainContentGeneratorConfig, 
  AgentState,
  LangChainToolWrapper,
  LangGraphMongoConfig,
  LangfuseConfig
} from './types/index.js';

// Core components
export { LangChainContentGenerator } from './core/contentGenerator.js';
export { StateGraphAgent } from './core/stateGraphAgent.js';
export { SessionManager } from './core/sessionManager.js';
export type { SessionData } from './core/sessionManager.js';
export { FileCheckpointSaver } from './core/fileCheckpointSaver.js';

// Model factory
export {
  createChatModel,
  createEmbeddings,
  getModelDisplayName,
  validateModelName,
  getDefaultModel,
} from './core/modelFactory.js';

// Tools
export { 
  CoreToolWrapper, 
  LangChainToolRegistry 
} from './tools/toolRegistry.js';

// Re-export important types from core
export type {
  AuthType,
  ApprovalMode,
} from './types/index.js';

// Re-export CLI integration
export {
  useHaiCode,
  createCLIInstance,
  getLangChainSettings,
  setupLangChainEnvironment,
} from './cli-integration.js';

import { StateGraphAgent } from './core/stateGraphAgent.js';
import { logger } from './utils/logger.js';
import { CHECKPOINTER_CONFIG } from './config/mongo.js';
import { LANGFUSE_CONFIG } from './config/langfuse.js';
import { getUserInfo } from './utils/user.js';

// Global session mapping for interactive mode to actual sessionid
const sessionSet = new Set<string>();

type AgentParams = import('@google/gemini-cli-core').ConfigParameters & {
  baseURL?: string;
  authType?: import('./types/index.js').ExtendedAuthType;
  checkpointer?: boolean;
  userId?: string;
  useGoogle?: boolean;
}

/**
 * Create a complete LangChain-based Gemini CLI instance
 */
export async function createHaicodeAgent(params: AgentParams) {
  // Create configuration
  const { LangChainConfig } = await import('./config/config.js');
  const config = new LangChainConfig(params);
  await config.initialize();
  
  // Create session manager with targetDir
  const { SessionManager } = await import('./core/sessionManager.js');
  const sessionManager = new SessionManager(
    100, // maxSessions
    24 * 60 * 60 * 1000, // sessionTimeout (24 hours)
    true, // persistenceEnabled
  );
  
  // Create agent - StateGraphAgent
  const agentOptions: {
    checkpointer?: import('@langchain/langgraph').BaseCheckpointSaver;
    enablePersistence?: boolean;
    maxIterations?: number;
    callbacks?: Array<import('@langchain/core/callbacks/base').CallbackHandlerMethods>;
  } = { enablePersistence: true };

  // Optional: MongoDB checkpointer via @langchain/langgraph-checkpoint-mongodb
  if (params.checkpointer) {
    try {
      const client = new MongoClient(CHECKPOINTER_CONFIG.uri);
      await client.connect();
      const saverInstance = new MongoDBSaver({
        client,
        dbName: CHECKPOINTER_CONFIG.dbName,
        checkpointCollectionName: CHECKPOINTER_CONFIG.checkpointCollectionName,
        checkpointWritesCollectionName: CHECKPOINTER_CONFIG.checkpointWritesCollectionName,
      });
      agentOptions.checkpointer = saverInstance as unknown as import('@langchain/langgraph').BaseCheckpointSaver;
    } catch (e) {
      logger.warning('[Lang] MongoDB checkpointer not enabled:', e);
    }
  }

  // Langfuse callbacks via langfuse-langchain
  const handler = new CallbackHandler({
    publicKey: LANGFUSE_CONFIG.publicKey,
    secretKey: LANGFUSE_CONFIG.secretKey,
    baseUrl: LANGFUSE_CONFIG.baseUrl,
    userId: params.userId || getUserInfo().userName,
    sessionId: params.sessionId,
    // release: params.langfuse.release,
    // version: params.langfuse.version,
    // updateRoot: params.langfuse.updateRoot,
  } as Record<string, unknown>);
  agentOptions.callbacks = [handler as unknown as import('@langchain/core/callbacks/base').CallbackHandlerMethods];

  const agent = new StateGraphAgent(
    config,
    agentOptions,
  );
  
  return {
    config,
    sessionManager,
    agent,
    
    // Expose LangChain content generator
    getContentGenerator(): import('./core/contentGenerator.js').LangChainContentGenerator {
      return config.getLangChainClient();
    },

    // Convenience methods
    async processMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): Promise<string> {
      const id = sessionId || sessionManager.createSession(userMemory);
      
      // Get current session state to track what we had before
      const currentSession = sessionManager.getSession(id);
      const beforeMessages = currentSession ? [...currentSession.messages] : [];

      // Let LangGraph manage conversation history completely - don't pass history
      // This avoids state conflicts between SessionManager and LangGraph MemorySaver
      const response = await agent.processMessage(userMessage, id, userMemory, []);

      // Get the final state from the agent's graph to capture all new messages
      const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

      // Extract only the truly new messages by comparing with what we had before
      if (finalState && finalState.values && finalState.values.messages) {
        const allMessages = finalState.values.messages;
        
        // Find messages that are genuinely new (not in our session before)
        const newMessages = allMessages.filter((msg: BaseMessage) => 
          !beforeMessages.some(existingMsg => 
            existingMsg.content === msg.content && 
            existingMsg.constructor.name === msg.constructor.name
          )
        );

        // Add only truly new messages to session manager
        if (newMessages.length > 0) {
          sessionManager.updateSession(id, newMessages);
          logger.debug(`[processMessage] Added ${newMessages.length} new messages to session ${id}`);
        } else {
          logger.debug(`[processMessage] No new messages to add for session ${id}`);
        }
      } else {
        // Fallback: manually add user message and AI response if we can't get the full state
        const { HumanMessage, AIMessage } = await import('@langchain/core/messages');
        const messagesToAdd = [new HumanMessage(userMessage)];
        
        if (response.trim()) {
          messagesToAdd.push(new AIMessage(response));
        }
        
        sessionManager.updateSession(id, messagesToAdd);
        logger.debug(`[processMessage] Added ${messagesToAdd.length} messages to session ${id} (fallback)`);
      }

      return response;
    },
    
    async *streamMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): AsyncGenerator<string> {
      let id: string;

      if (sessionId) {
        // Check if we have a mapped session for this interactive session
        if (sessionSet.has(sessionId)) {
          id = sessionId;
        } else {
          // Create a new session and map it to the interactive session ID
          id = sessionManager.createSession(userMemory, sessionId);
          sessionSet.add(sessionId);
          logger.debug(`[CLI] Mapped interactive session ${sessionId} to actual session ${id}`);
        }
      } else {
        id = sessionManager.createSession(userMemory);
      }

      // Get current session state to track what we had before
      const currentSession = sessionManager.getSession(id);
      const beforeMessages = currentSession ? [...currentSession.messages] : [];

      let responseContent = '';

      // Let LangGraph manage conversation history completely - don't pass history
      // This avoids state conflicts between SessionManager and LangGraph MemorySaver
      for await (const chunk of agent.streamMessage(userMessage, id, userMemory, [])) {
        responseContent += chunk;
        yield chunk;
      }

      // Get the final state from the agent's graph to capture all new messages
      try {
        const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

        // Extract only the truly new messages by comparing with what we had before
        if (finalState && finalState.values && finalState.values.messages) {
          const allMessages = finalState.values.messages;
          
          // Find messages that are genuinely new (not in our session before)
          const newMessages = allMessages.filter((msg: BaseMessage) => 
            !beforeMessages.some(existingMsg => 
              existingMsg.content === msg.content && 
              existingMsg.constructor.name === msg.constructor.name
            )
          );

          // Add only truly new messages to session manager
          if (newMessages.length > 0) {
            sessionManager.updateSession(id, newMessages);
            logger.debug(`[streamMessage] Added ${newMessages.length} new messages to session ${id}`);
          } else {
            logger.debug(`[streamMessage] No new messages to add for session ${id}`);
          }
        } else {
          // Fallback: manually add user message and AI response if we can't get the full state
          const { HumanMessage, AIMessage } = await import('@langchain/core/messages');
          const messagesToAdd = [new HumanMessage(userMessage)];
          
          if (responseContent.trim()) {
            messagesToAdd.push(new AIMessage(responseContent));
          }
          
          sessionManager.updateSession(id, messagesToAdd);
          logger.debug(`[streamMessage] Added ${messagesToAdd.length} messages to session ${id} (fallback)`);
        }
      } catch (error) {
        logger.error('[streamMessage] Failed to get final state from agent:', error);
        // Fallback: manually add user message and AI response
        const { HumanMessage, AIMessage } = await import('@langchain/core/messages');
        const messagesToAdd = [new HumanMessage(userMessage)];
        
        if (responseContent.trim()) {
          messagesToAdd.push(new AIMessage(responseContent));
        }
        
        sessionManager.updateSession(id, messagesToAdd);
        logger.debug(`[streamMessage] Added ${messagesToAdd.length} messages to session ${id} (error fallback)`);
      }
    },
  };
}
