/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

import { SessionManager } from './sessionManager.js';

describe('SessionManager', () => {
  let sessionManager: SessionManager;

  beforeEach(() => {
    sessionManager = new SessionManager(10, 1000); // Small limits for testing
  });

  describe('createSession', () => {
    it('should create a new session with unique ID', () => {
      const sessionId1 = sessionManager.createSession();
      const sessionId2 = sessionManager.createSession();

      expect(sessionId1).toBeDefined();
      expect(sessionId2).toBeDefined();
      expect(sessionId1).not.toBe(sessionId2);
    });

    it('should create session with user memory', () => {
      const userMemory = 'User prefers TypeScript';
      const sessionId = sessionManager.createSession(userMemory);
      const session = sessionManager.getSession(sessionId);

      expect(session?.userMemory).toBe(userMemory);
    });

    it('should create session with metadata', () => {
      const metadata = { userId: '123', project: 'test' };
      const sessionId = sessionManager.createSession(undefined, metadata);
      const session = sessionManager.getSession(sessionId);

      expect(session?.metadata).toEqual(metadata);
    });

    it('should update last activity when creating session', () => {
      const before = new Date();
      const sessionId = sessionManager.createSession();
      const session = sessionManager.getSession(sessionId);
      const after = new Date();

      expect(session?.createdAt.getTime()).toBeGreaterThanOrEqual(before.getTime());
      expect(session?.createdAt.getTime()).toBeLessThanOrEqual(after.getTime());
    });
  });

  describe('getSession', () => {
    it('should return session by ID', () => {
      const sessionId = sessionManager.createSession();
      const session = sessionManager.getSession(sessionId);

      expect(session).toBeDefined();
      expect(session?.id).toBe(sessionId);
    });

    it('should return undefined for non-existent session', () => {
      const session = sessionManager.getSession('non-existent');
      expect(session).toBeUndefined();
    });

    it('should update last activity when getting session', () => {
      const sessionId = sessionManager.createSession();
      const session1 = sessionManager.getSession(sessionId);
      const lastActivity1 = session1?.lastActivity.getTime();

      // Wait a bit
      setTimeout(() => {}, 10);

      const session2 = sessionManager.getSession(sessionId);
      const lastActivity2 = session2?.lastActivity.getTime();

      expect(lastActivity2).toBeGreaterThan(lastActivity1!);
    });
  });

  describe('updateSession', () => {
    it('should update session with new messages', () => {
      const sessionId = sessionManager.createSession();
      const messages = [new HumanMessage('Hello'), new AIMessage('Hi there!')];

      const result = sessionManager.updateSession(sessionId, messages);
      const session = sessionManager.getSession(sessionId);

      expect(result).toBe(true);
      expect(session?.messages).toHaveLength(2);
    });

    it('should update user memory', () => {
      const sessionId = sessionManager.createSession();
      const newMemory = 'Updated user memory';

      const result = sessionManager.updateSession(sessionId, [], newMemory);
      const session = sessionManager.getSession(sessionId);

      expect(result).toBe(true);
      expect(session?.userMemory).toBe(newMemory);
    });

    it('should return false for non-existent session', () => {
      const result = sessionManager.updateSession('non-existent', []);
      expect(result).toBe(false);
    });
  });

  describe('addMessage', () => {
    it('should add single message to session', () => {
      const sessionId = sessionManager.createSession();
      const message = new HumanMessage('Test message');

      const result = sessionManager.addMessage(sessionId, message);
      const session = sessionManager.getSession(sessionId);

      expect(result).toBe(true);
      expect(session?.messages).toHaveLength(1);
      expect(session?.messages[0]).toBe(message);
    });

    it('should return false for non-existent session', () => {
      const result = sessionManager.addMessage('non-existent', new HumanMessage('test'));
      expect(result).toBe(false);
    });
  });

  describe('getConversationHistory', () => {
    it('should return conversation history', () => {
      const sessionId = sessionManager.createSession();
      const messages = [new HumanMessage('Hello'), new AIMessage('Hi')];
      sessionManager.updateSession(sessionId, messages);

      const history = sessionManager.getConversationHistory(sessionId);

      expect(history).toHaveLength(2);
      expect(history[0]).toBeInstanceOf(HumanMessage);
      expect(history[1]).toBeInstanceOf(AIMessage);
    });

    it('should return empty array for non-existent session', () => {
      const history = sessionManager.getConversationHistory('non-existent');
      expect(history).toEqual([]);
    });
  });

  describe('getUserMemory', () => {
    it('should return user memory', () => {
      const userMemory = 'User memory test';
      const sessionId = sessionManager.createSession(userMemory);

      const memory = sessionManager.getUserMemory(sessionId);

      expect(memory).toBe(userMemory);
    });

    it('should return undefined for non-existent session', () => {
      const memory = sessionManager.getUserMemory('non-existent');
      expect(memory).toBeUndefined();
    });
  });

  describe('updateUserMemory', () => {
    it('should update user memory', () => {
      const sessionId = sessionManager.createSession();
      const newMemory = 'Updated memory';

      const result = sessionManager.updateUserMemory(sessionId, newMemory);
      const memory = sessionManager.getUserMemory(sessionId);

      expect(result).toBe(true);
      expect(memory).toBe(newMemory);
    });

    it('should return false for non-existent session', () => {
      const result = sessionManager.updateUserMemory('non-existent', 'test');
      expect(result).toBe(false);
    });
  });

  describe('deleteSession', () => {
    it('should delete existing session', () => {
      const sessionId = sessionManager.createSession();
      
      const result = sessionManager.deleteSession(sessionId);
      const session = sessionManager.getSession(sessionId);

      expect(result).toBe(true);
      expect(session).toBeUndefined();
    });

    it('should return false for non-existent session', () => {
      const result = sessionManager.deleteSession('non-existent');
      expect(result).toBe(false);
    });
  });

  describe('getActiveSessions', () => {
    it('should return all active session IDs', () => {
      const sessionId1 = sessionManager.createSession();
      const sessionId2 = sessionManager.createSession();

      const activeSessions = sessionManager.getActiveSessions();

      expect(activeSessions).toContain(sessionId1);
      expect(activeSessions).toContain(sessionId2);
      expect(activeSessions).toHaveLength(2);
    });

    it('should return empty array when no sessions', () => {
      const activeSessions = sessionManager.getActiveSessions();
      expect(activeSessions).toEqual([]);
    });
  });

  describe('getSessionCount', () => {
    it('should return correct session count', () => {
      expect(sessionManager.getSessionCount()).toBe(0);

      sessionManager.createSession();
      expect(sessionManager.getSessionCount()).toBe(1);

      sessionManager.createSession();
      expect(sessionManager.getSessionCount()).toBe(2);
    });
  });

  describe('getSessionMetadata', () => {
    it('should return session metadata', () => {
      const metadata = { userId: '123', project: 'test' };
      const sessionId = sessionManager.createSession(undefined, metadata);

      const retrievedMetadata = sessionManager.getSessionMetadata(sessionId);

      expect(retrievedMetadata).toEqual(metadata);
    });

    it('should return undefined for non-existent session', () => {
      const metadata = sessionManager.getSessionMetadata('non-existent');
      expect(metadata).toBeUndefined();
    });
  });

  describe('updateSessionMetadata', () => {
    it('should update session metadata', () => {
      const sessionId = sessionManager.createSession();
      const newMetadata = { updated: true, timestamp: Date.now() };

      const result = sessionManager.updateSessionMetadata(sessionId, newMetadata);
      const metadata = sessionManager.getSessionMetadata(sessionId);

      expect(result).toBe(true);
      expect(metadata).toEqual(newMetadata);
    });

    it('should merge metadata', () => {
      const initialMetadata = { userId: '123' };
      const sessionId = sessionManager.createSession(undefined, initialMetadata);
      const additionalMetadata = { project: 'test' };

      sessionManager.updateSessionMetadata(sessionId, additionalMetadata);
      const metadata = sessionManager.getSessionMetadata(sessionId);

      expect(metadata).toEqual({ userId: '123', project: 'test' });
    });

    it('should return false for non-existent session', () => {
      const result = sessionManager.updateSessionMetadata('non-existent', {});
      expect(result).toBe(false);
    });
  });

  describe('getSessionStats', () => {
    it('should return session statistics', () => {
      const sessionId = sessionManager.createSession();
      const messages = [
        new HumanMessage('Hello'),
        new AIMessage('Hi'),
        new HumanMessage('How are you?'),
      ];
      sessionManager.updateSession(sessionId, messages);

      const stats = sessionManager.getSessionStats(sessionId);

      expect(stats).toBeDefined();
      expect(stats?.messageCount).toBe(3);
      expect(stats?.userMessageCount).toBe(2);
      expect(stats?.aiMessageCount).toBe(1);
      expect(stats?.sessionDuration).toBeGreaterThan(0);
    });

    it('should return undefined for non-existent session', () => {
      const stats = sessionManager.getSessionStats('non-existent');
      expect(stats).toBeUndefined();
    });
  });

  describe('sessionToAgentState', () => {
    it('should convert session to AgentState', () => {
      const userMemory = 'User memory';
      const sessionId = sessionManager.createSession(userMemory);
      const messages = [new HumanMessage('Hello')];
      sessionManager.updateSession(sessionId, messages);

      const agentState = sessionManager.sessionToAgentState(sessionId);

      expect(agentState).toBeDefined();
      expect(agentState?.sessionId).toBe(sessionId);
      expect(agentState?.userMemory).toBe(userMemory);
      expect(agentState?.messages).toHaveLength(1);
      expect(agentState?.isComplete).toBe(false);
    });

    it('should return undefined for non-existent session', () => {
      const agentState = sessionManager.sessionToAgentState('non-existent');
      expect(agentState).toBeUndefined();
    });
  });

  describe('updateSessionFromAgentState', () => {
    it('should update session from AgentState', () => {
      const sessionId = sessionManager.createSession();
      const agentState = {
        messages: [new HumanMessage('Hello'), new AIMessage('Hi')],
        sessionId,
        userMemory: 'Updated memory',
        isComplete: false,
        toolResults: [],
      };

      const result = sessionManager.updateSessionFromAgentState(sessionId, agentState);
      const session = sessionManager.getSession(sessionId);

      expect(result).toBe(true);
      expect(session?.messages).toHaveLength(2);
      expect(session?.userMemory).toBe('Updated memory');
    });

    it('should return false for non-existent session', () => {
      const agentState = {
        messages: [],
        sessionId: 'non-existent',
        isComplete: false,
        toolResults: [],
      };

      const result = sessionManager.updateSessionFromAgentState('non-existent', agentState);
      expect(result).toBe(false);
    });
  });

  describe('exportSession', () => {
    it('should export session data', () => {
      const userMemory = 'User memory';
      const metadata = { userId: '123' };
      const sessionId = sessionManager.createSession(userMemory, metadata);
      const messages = [new HumanMessage('Hello')];
      sessionManager.updateSession(sessionId, messages);

      const exported = sessionManager.exportSession(sessionId);

      expect(exported).toBeDefined();
      expect(typeof exported).toBe('string');
      
      const parsed = JSON.parse(exported!);
      expect(parsed.id).toBe(sessionId);
      expect(parsed.userMemory).toBe(userMemory);
      expect(parsed.metadata).toEqual(metadata);
      expect(parsed.messages).toHaveLength(1);
    });

    it('should return undefined for non-existent session', () => {
      const exported = sessionManager.exportSession('non-existent');
      expect(exported).toBeUndefined();
    });
  });

  describe('importSession', () => {
    it('should import session data', () => {
      const sessionData = {
        id: 'imported-session',
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        messages: [
          { type: 'HumanMessage', content: 'Hello', additional_kwargs: {} },
          { type: 'AIMessage', content: 'Hi', additional_kwargs: {} },
        ],
        userMemory: 'Imported memory',
        metadata: { imported: true },
      };

      const sessionId = sessionManager.importSession(JSON.stringify(sessionData));

      expect(sessionId).toBe('imported-session');
      
      const session = sessionManager.getSession(sessionId!);
      expect(session).toBeDefined();
      expect(session?.userMemory).toBe('Imported memory');
      expect(session?.messages).toHaveLength(2);
    });

    it('should return undefined for invalid JSON', () => {
      const sessionId = sessionManager.importSession('invalid json');
      expect(sessionId).toBeUndefined();
    });
  });

  describe('clearAllSessions', () => {
    it('should clear all sessions', () => {
      sessionManager.createSession();
      sessionManager.createSession();
      expect(sessionManager.getSessionCount()).toBe(2);

      sessionManager.clearAllSessions();
      expect(sessionManager.getSessionCount()).toBe(0);
    });
  });
}); 