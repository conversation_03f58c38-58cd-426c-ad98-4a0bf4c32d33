/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'node:path';
import { promises as fs } from 'node:fs';
import type { RunnableConfig } from '@langchain/core/runnables';
import { BaseCheckpointSaver } from '@langchain/langgraph-checkpoint';
import type { Checkpoint, CheckpointTuple, CheckpointListOptions, ChannelVersions } from '@langchain/langgraph-checkpoint';
import type { PendingWrite, CheckpointPendingWrite, CheckpointMetadata } from '@langchain/langgraph-checkpoint';
import { getProjectTempDir } from '../utils/paths.js';
import { logger as log } from '../utils/logger.js';

interface StoredCheckpointFile {
  checkpoint: Checkpoint;
  metadata: CheckpointMetadata;
  newVersions?: ChannelVersions;
  parentConfig?: RunnableConfig;
}

interface StoredWritesFile {
  taskId: string;
  writes: PendingWrite[];
  checkpointId?: string;
}

function getConfigThreadId(config: RunnableConfig): string {
  const cfg = (config?.configurable ?? {}) as { thread_id?: string };
  if (!cfg.thread_id || typeof cfg.thread_id !== 'string') {
    throw new Error('RunnableConfig.configurable.thread_id must be a non-empty string');
  }
  return cfg.thread_id;
}

function withCheckpointId(config: RunnableConfig, checkpointId: string): RunnableConfig {
  const configurable = { ...(config.configurable ?? {}) } as Record<string, unknown>;
  configurable.checkpoint_id = checkpointId;
  return { ...config, configurable } as RunnableConfig;
}

export class FileCheckpointSaver extends BaseCheckpointSaver<number> {
  private baseDir: string;

  constructor(options?: { baseDir?: string }) {
    super();
    const projectRoot = process.cwd();
    const projectTempDir = getProjectTempDir(projectRoot);
    this.baseDir = options?.baseDir ?? path.join(projectTempDir, 'langgraph');
  }

  private threadDir(threadId: string): string {
    return path.join(this.baseDir, threadId);
  }

  private checkpointsDir(threadId: string): string {
    return path.join(this.threadDir(threadId), 'checkpoints');
  }

  private writesDir(threadId: string): string {
    return path.join(this.threadDir(threadId), 'writes');
  }

  private async ensureDirs(threadId: string): Promise<void> {
    await fs.mkdir(this.checkpointsDir(threadId), { recursive: true });
    await fs.mkdir(this.writesDir(threadId), { recursive: true });
  }

  private checkpointFilePath(threadId: string, checkpointId: string): string {
    return path.join(this.checkpointsDir(threadId), `checkpoint-${checkpointId}.json`);
  }

  private async readStoredCheckpoint(threadId: string, filePath: string): Promise<StoredCheckpointFile | undefined> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const parsed = JSON.parse(content) as StoredCheckpointFile;
      // Basic validation
      if (parsed && parsed.checkpoint && parsed.checkpoint.id) {
        return parsed;
      }
      return undefined;
    } catch (_err) {
      log.warning('[FileCheckpointSaver] Failed to read checkpoint file:', _err);
      return undefined;
    }
  }

  private async listCheckpointFiles(threadId: string): Promise<string[]> {
    try {
      const dir = this.checkpointsDir(threadId);
      const files = await fs.readdir(dir);
      const fullPaths = files
        .filter((f) => f.startsWith('checkpoint-') && f.endsWith('.json'))
        .map((f) => path.join(dir, f));

      // Sort by mtime desc (newest first)
      const stats = await Promise.all(
        fullPaths.map(async (p) => ({ p, s: await fs.stat(p) }))
      );
      return stats.sort((a, b) => b.s.mtimeMs - a.s.mtimeMs).map(({ p }) => p);
    } catch (_err) {
      return [];
    }
  }

  async getTuple(config: RunnableConfig): Promise<CheckpointTuple | undefined> {
    const threadId = getConfigThreadId(config);
    await this.ensureDirs(threadId);

    const cfg = (config.configurable ?? {}) as { checkpoint_id?: string };
    let stored: StoredCheckpointFile | undefined;

    if (cfg.checkpoint_id && typeof cfg.checkpoint_id === 'string') {
      const fp = this.checkpointFilePath(threadId, cfg.checkpoint_id);
      stored = await this.readStoredCheckpoint(threadId, fp);
    } else {
      const files = await this.listCheckpointFiles(threadId);
      for (const fp of files) {
        const candidate = await this.readStoredCheckpoint(threadId, fp);
        if (candidate) {
          stored = candidate;
          break;
        }
      }
    }

    if (!stored) return undefined;

    const enriched = withCheckpointId(config, stored.checkpoint.id);
    const tuple: CheckpointTuple = {
      config: enriched,
      checkpoint: stored.checkpoint,
      metadata: stored.metadata,
      parentConfig: stored.parentConfig,
      pendingWrites: [] as CheckpointPendingWrite[],
    };
    return tuple;
  }

  async *list(config: RunnableConfig, options?: CheckpointListOptions): AsyncGenerator<CheckpointTuple> {
    const threadId = getConfigThreadId(config);
    const limit = options?.limit ?? Infinity;
    let yielded = 0;

    const files = await this.listCheckpointFiles(threadId);
    for (const fp of files) {
      if (yielded >= limit) break;
      const stored = await this.readStoredCheckpoint(threadId, fp);
      if (!stored) continue;
      const enriched = withCheckpointId(config, stored.checkpoint.id);
      yield {
        config: enriched,
        checkpoint: stored.checkpoint,
        metadata: stored.metadata,
        parentConfig: stored.parentConfig,
        pendingWrites: [] as CheckpointPendingWrite[],
      };
      yielded += 1;
    }
  }

  async put(
    config: RunnableConfig,
    checkpoint: Checkpoint,
    metadata: CheckpointMetadata,
    newVersions: ChannelVersions
  ): Promise<RunnableConfig> {
    const threadId = getConfigThreadId(config);
    await this.ensureDirs(threadId);

    const filePath = this.checkpointFilePath(threadId, checkpoint.id);
    const payload: StoredCheckpointFile = {
      checkpoint,
      metadata,
      newVersions,
      parentConfig: config,
    };
    await fs.writeFile(filePath, JSON.stringify(payload, null, 2), 'utf-8');

    const enriched = withCheckpointId(config, checkpoint.id);
    log.debug(`[FileCheckpointSaver] Saved checkpoint ${checkpoint.id} for thread ${threadId}`);
    return enriched;
  }

  async putWrites(config: RunnableConfig, writes: PendingWrite[], taskId: string): Promise<void> {
    const threadId = getConfigThreadId(config);
    await this.ensureDirs(threadId);
    const cfg = (config.configurable ?? {}) as { checkpoint_id?: string };
    const filePath = path.join(this.writesDir(threadId), `writes-${taskId}.json`);
    const payload: StoredWritesFile = {
      taskId,
      writes,
      checkpointId: typeof cfg.checkpoint_id === 'string' ? cfg.checkpoint_id : undefined,
    };
    await fs.writeFile(filePath, JSON.stringify(payload, null, 2), 'utf-8');
    log.debug(`[FileCheckpointSaver] Saved writes for task ${taskId} (thread ${threadId})`);
  }

  // Some versions of BaseCheckpointSaver include deleteThread; implement as a safe no-op
  async deleteThread(_threadId: string): Promise<void> {
    // Intentionally no-op to avoid accidental data deletion
  }
}


