/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseMessage, AIMessage, HumanMessage } from '@langchain/core/messages';
import type { BaseCheckpointSaver } from '@langchain/langgraph';
import logger from '../utils/logger.js';

/**
 * Represents a single turn in a conversation
 */
export interface ConversationTurn {
  id: string;
  sessionId: string;
  timestamp: Date;
  userMessage: HumanMessage;
  assistantMessage?: AIMessage;
  metadata: {
    duration?: number;
    tokenUsage?: {
      input: number;
      output: number;
      total: number;
    };
    feedback?: 'positive' | 'negative' | 'neutral';
    context?: Record<string, unknown>;
    compressed?: boolean;
    originalTurnCount?: number;
    compressionTimestamp?: number;
    compressionRatio?: number;
    error?: string;
  };
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
}

/**
 * Configuration for turn management
 */
export interface TurnManagerConfig {
  maxTurnsPerSession: number;
  turnTimeoutMs: number;
  enableTurnLogging: boolean;
  enableTurnCompression: boolean;
  compressionThreshold: number;
}

/**
 * Advanced turn management for LangGraph conversations
 * Provides fine-grained control over conversation sessions and turns
 */
export class LangChainTurnManager {
  private turns: Map<string, ConversationTurn[]> = new Map();
  private activeTurns: Map<string, ConversationTurn> = new Map();
  private config: TurnManagerConfig;
  private checkpointer?: BaseCheckpointSaver;

  constructor(
    config: Partial<TurnManagerConfig> = {},
    checkpointer?: BaseCheckpointSaver
  ) {
    this.config = {
      maxTurnsPerSession: config.maxTurnsPerSession || 100,
      turnTimeoutMs: config.turnTimeoutMs || 300000, // 5 minutes
      enableTurnLogging: config.enableTurnLogging !== false,
      enableTurnCompression: config.enableTurnCompression !== false,
      compressionThreshold: config.compressionThreshold || 50,
    };
    this.checkpointer = checkpointer;
  }

  /**
   * Start a new conversation turn
   */
  async startTurn(
    sessionId: string,
    userMessage: HumanMessage,
    context?: Record<string, unknown>
  ): Promise<ConversationTurn> {
    const turnId = this.generateTurnId(sessionId);
    
    const turn: ConversationTurn = {
      id: turnId,
      sessionId,
      timestamp: new Date(),
      userMessage,
      metadata: {
        context,
      },
      status: 'pending',
    };

    // Store the active turn
    this.activeTurns.set(turnId, turn);

    // Add to session history
    if (!this.turns.has(sessionId)) {
      this.turns.set(sessionId, []);
    }
    const sessionTurns = this.turns.get(sessionId)!;
    sessionTurns.push(turn);

    // Check if we need to compress old turns
    if (sessionTurns.length > this.config.compressionThreshold) {
      await this.compressTurns(sessionId);
    }

    // Set timeout for turn
    setTimeout(() => {
      if (this.activeTurns.has(turnId) && this.activeTurns.get(turnId)?.status === 'pending') {
        this.timeoutTurn(turnId);
      }
    }, this.config.turnTimeoutMs);

    if (this.config.enableTurnLogging) {
      logger.info(`[TurnManager] Started turn ${turnId} for session ${sessionId}`);
    }

    return turn;
  }

  /**
   * Complete a conversation turn with the assistant's response
   */
  async completeTurn(
    turnId: string,
    assistantMessage: AIMessage,
    metadata?: Partial<ConversationTurn['metadata']>
  ): Promise<ConversationTurn> {
    const turn = this.activeTurns.get(turnId);
    if (!turn) {
      throw new Error(`Turn ${turnId} not found or already completed`);
    }

    const completedTurn: ConversationTurn = {
      ...turn,
      assistantMessage,
      metadata: {
        ...turn.metadata,
        ...metadata,
        duration: Date.now() - turn.timestamp.getTime(),
      },
      status: 'completed',
    };

    // Update in session history
    const sessionTurns = this.turns.get(turn.sessionId);
    if (sessionTurns) {
      const index = sessionTurns.findIndex(t => t.id === turnId);
      if (index !== -1) {
        sessionTurns[index] = completedTurn;
      }
    }

    // Remove from active turns
    this.activeTurns.delete(turnId);

    // Persist to checkpointer if available
    if (this.checkpointer) {
      await this.persistTurn(completedTurn);
    }

    if (this.config.enableTurnLogging) {
      logger.success(`[TurnManager] Completed turn ${turnId} in ${completedTurn.metadata.duration}ms`);
    }

    return completedTurn;
  }

  /**
   * Fail a conversation turn
   */
  async failTurn(turnId: string, error: Error): Promise<ConversationTurn> {
    const turn = this.activeTurns.get(turnId);
    if (!turn) {
      throw new Error(`Turn ${turnId} not found`);
    }

    const failedTurn: ConversationTurn = {
      ...turn,
      metadata: {
        ...turn.metadata,
        duration: Date.now() - turn.timestamp.getTime(),
        error: error.message,
      },
      status: 'failed',
    };

    // Update in session history
    const sessionTurns = this.turns.get(turn.sessionId);
    if (sessionTurns) {
      const index = sessionTurns.findIndex(t => t.id === turnId);
      if (index !== -1) {
        sessionTurns[index] = failedTurn;
      }
    }

    // Remove from active turns
    this.activeTurns.delete(turnId);

    if (this.config.enableTurnLogging) {
      logger.error(`[TurnManager] Failed turn ${turnId}: ${error.message}`);
    }

    return failedTurn;
  }

  /**
   * Get all turns for a session
   */
  getSessionTurns(sessionId: string): ConversationTurn[] {
    return this.turns.get(sessionId) || [];
  }

  /**
   * Get active turn for a session (if any)
   */
  getActiveTurn(sessionId: string): ConversationTurn | undefined {
    for (const turn of this.activeTurns.values()) {
      if (turn.sessionId === sessionId) {
        return turn;
      }
    }
    return undefined;
  }

  /**
   * Get recent messages for context (excluding current turn)
   */
  getRecentMessages(sessionId: string, count: number = 10): BaseMessage[] {
    const sessionTurns = this.getSessionTurns(sessionId);
    const completedTurns = sessionTurns.filter(t => t.status === 'completed');
    
    // Get the last N completed turns
    const recentTurns = completedTurns.slice(-count);
    
    const messages: BaseMessage[] = [];
    for (const turn of recentTurns) {
      messages.push(turn.userMessage);
      if (turn.assistantMessage) {
        messages.push(turn.assistantMessage);
      }
    }
    
    return messages;
  }

  /**
   * Add feedback to a turn
   */
  async addTurnFeedback(
    turnId: string, 
    feedback: 'positive' | 'negative' | 'neutral'
  ): Promise<void> {
    const sessionTurns = Array.from(this.turns.values()).flat();
    const turn = sessionTurns.find(t => t.id === turnId);
    
    if (turn) {
      turn.metadata.feedback = feedback;
      
      // Persist feedback if checkpointer available
      if (this.checkpointer) {
        await this.persistTurn(turn);
      }
      
      if (this.config.enableTurnLogging) {
        logger.info(`[TurnManager] Added ${feedback} feedback to turn ${turnId}`);
      }
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId: string): {
    totalTurns: number;
    completedTurns: number;
    failedTurns: number;
    averageDuration: number;
    totalTokenUsage: number;
  } {
    const sessionTurns = this.getSessionTurns(sessionId);
    const completedTurns = sessionTurns.filter(t => t.status === 'completed');
    const failedTurns = sessionTurns.filter(t => t.status === 'failed');
    
    const totalDuration = completedTurns.reduce((sum, turn) => 
      sum + (turn.metadata.duration || 0), 0
    );
    
    const totalTokenUsage = completedTurns.reduce((sum, turn) => 
      sum + (turn.metadata.tokenUsage?.total || 0), 0
    );

    return {
      totalTurns: sessionTurns.length,
      completedTurns: completedTurns.length,
      failedTurns: failedTurns.length,
      averageDuration: completedTurns.length > 0 ? totalDuration / completedTurns.length : 0,
      totalTokenUsage,
    };
  }

  /**
   * Clear old sessions to free memory
   */
  async clearOldSessions(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    const now = Date.now();
    const sessionsToDelete: string[] = [];

    for (const [sessionId, turns] of this.turns.entries()) {
      const lastTurn = turns[turns.length - 1];
      if (lastTurn && (now - lastTurn.timestamp.getTime()) > maxAge) {
        sessionsToDelete.push(sessionId);
      }
    }

    for (const sessionId of sessionsToDelete) {
      this.turns.delete(sessionId);
      if (this.config.enableTurnLogging) {
        logger.info(`[TurnManager] Cleared old session ${sessionId}`);
      }
    }
  }

  /**
   * Generate a unique turn ID
   */
  private generateTurnId(sessionId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${sessionId}_${timestamp}_${random}`;
  }

  /**
   * Handle turn timeout
   */
  private async timeoutTurn(turnId: string): Promise<void> {
    const turn = this.activeTurns.get(turnId);
    if (turn) {
      await this.failTurn(turnId, new Error('Turn timeout'));
    }
  }

  /**
   * Compress old turns in a session to save memory
   */
  private async compressTurns(sessionId: string): Promise<void> {
    if (!this.config.enableTurnCompression) {
      return;
    }

    const sessionTurns = this.turns.get(sessionId);
    if (!sessionTurns || sessionTurns.length <= this.config.compressionThreshold) {
      return;
    }

    // Keep recent turns, compress older ones
    const recentTurns = sessionTurns.slice(-this.config.compressionThreshold);
    const oldTurns = sessionTurns.slice(0, -this.config.compressionThreshold);

    // Create a compressed summary of old turns
    const compressedSummary = {
      id: `compressed_${sessionId}_${Date.now()}`,
      sessionId,
      timestamp: oldTurns[0]?.timestamp || new Date(),
      userMessage: new HumanMessage({
        content: `[Compressed summary of ${oldTurns.length} previous turns]`,
      }),
      assistantMessage: new AIMessage({
        content: `Summary of previous conversation: ${oldTurns.length} turns completed.`,
      }),
      metadata: {
        compressed: true,
        originalTurnCount: oldTurns.length,
        compressionTimestamp: Date.now(),
      },
      status: 'completed' as const,
    };

    // Replace old turns with compressed summary
    this.turns.set(sessionId, [compressedSummary, ...recentTurns]);

    if (this.config.enableTurnLogging) {
      logger.info(`[TurnManager] Compressed ${oldTurns.length} turns for session ${sessionId}`);
    }
  }

  /**
   * Persist turn to checkpointer
   */
  private async persistTurn(turn: ConversationTurn): Promise<void> {
    if (!this.checkpointer) {
      return;
    }

    try {
      // Implementation would depend on the specific checkpointer interface
      // This is a placeholder for the persistence logic
      if (this.config.enableTurnLogging) {
        logger.debug(`[TurnManager] Persisted turn ${turn.id} to checkpointer`);
      }
    } catch (error) {
      logger.error(`[TurnManager] Failed to persist turn ${turn.id}:`, error);
    }
  }
}