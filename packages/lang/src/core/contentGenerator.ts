/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { Embeddings } from '@langchain/core/embeddings';
import { Tool } from '@langchain/core/tools';
import { 
  AIMessage, 
  HumanMessage, 
  SystemMessage, 
  BaseMessage
} from '@langchain/core/messages';

// Define our own types to avoid core package dependencies
export interface ContentPart {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string;
  };
}

export interface Content {
  role?: 'user' | 'model';
  parts: ContentPart[];
}

export interface GenerateContentParameters {
  contents?: Content[];
  systemInstruction?: Content;
  tools?: Tool[];
  generationConfig?: {
    temperature?: number;
    maxOutputTokens?: number;
    topP?: number;
    topK?: number;
  };
}

export interface GenerateContentResponse {
  candidates: Array<{
    content: Content;
    finishReason: 'STOP' | 'MAX_TOKENS' | 'SAFETY' | 'RECITATION';
    index: number;
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

export interface CountTokensParameters {
  contents: Content[];
}

export interface CountTokensResponse {
  totalTokens: number;
}

export interface EmbedContentParameters {
  content: Content;
  taskType?: string;
  title?: string;
}

export interface EmbedContentResponse {
  embedding: {
    values: number[];
  };
}

/**
 * LangChain-based content generator that implements the same interface
 * as the core ContentGenerator but uses LangChain models internally.
 */
export class LangChainContentGenerator {
  constructor(
    private chatModel: BaseChatModel,
    private embeddings: Embeddings,
    private tools: Tool[]
  ) {}

  /**
   * Generate content using LangChain chat model
   */
  async generateContent(
    request: GenerateContentParameters
  ): Promise<GenerateContentResponse> {
    try {
      const messages = this.convertContentsToMessages(request.contents || []);
      
      // Add system instruction if provided
      if (request.systemInstruction) {
        const systemContent = this.extractTextFromContent(request.systemInstruction);
        messages.unshift(new SystemMessage(systemContent));
      }

      // Configure tools if provided
      const modelWithTools = request.tools && request.tools.length > 0 && this.chatModel.bindTools
        ? this.chatModel.bindTools(this.tools)
        : this.chatModel;

      // Generate response
      const result = await modelWithTools.invoke(messages);

      // Convert LangChain response to our format
      return this.convertToGenerateContentResponse(result, request);
    } catch (error) {
      throw new Error(`Content generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate streaming content using LangChain chat model
   */
  async generateContentStream(
    request: GenerateContentParameters
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const messages = this.convertContentsToMessages(request.contents || []);
    
    // Add system instruction if provided
    if (request.systemInstruction) {
      const systemContent = this.extractTextFromContent(request.systemInstruction);
      messages.unshift(new SystemMessage(systemContent));
    }

    // Configure tools if provided
    const modelWithTools = request.tools && request.tools.length > 0 && this.chatModel.bindTools
      ? this.chatModel.bindTools(this.tools)
      : this.chatModel;

    const stream = await modelWithTools.stream(messages);

    return this.convertStreamToGenerateContentResponse(stream, request);
  }

  /**
   * Count tokens in the input
   */
  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    try {
      const messages = this.convertContentsToMessages(request.contents);
      const text = messages.map(msg => msg.content).join(' ');
      
      // Estimate token count (rough approximation)
      const estimatedTokens = Math.ceil(text.length / 4);
      
      return {
        totalTokens: estimatedTokens
      };
    } catch (error) {
      throw new Error(`Token counting failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate embeddings for content
   */
  async embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse> {
    try {
      const text = this.extractTextFromContent(request.content);
      const embeddings = await this.embeddings.embedQuery(text);
      
      return {
        embedding: {
          values: embeddings
        }
      };
    } catch (error) {
      throw new Error(`Embedding generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Convert content array to LangChain messages
   */
  private convertContentsToMessages(contents: Content[]): BaseMessage[] {
    return contents.map(content => {
      const text = this.extractTextFromContent(content);
      
      if (content.role === 'model') {
        return new AIMessage(text);
      } else {
        return new HumanMessage(text);
      }
    });
  }

  /**
   * Extract text from content
   */
  private extractTextFromContent(content: Content): string {
    return content.parts
      .filter((part): part is { text: string } => 'text' in part)
      .map(part => part.text)
      .join('');
  }

  /**
   * Convert LangChain response to our format
   */
  private convertToGenerateContentResponse(
    result: BaseMessage,
    _request: GenerateContentParameters
  ): GenerateContentResponse {
    const content: Content = {
      role: 'model',
      parts: [{ text: typeof result.content === 'string' ? result.content : String(result.content) }]
    };

    return {
      candidates: [{
        content,
        finishReason: 'STOP',
        index: 0
      }],
      usageMetadata: {
        promptTokenCount: 0, // We don't have accurate token counting
        candidatesTokenCount: 0,
        totalTokenCount: 0
      }
    };
  }

  /**
   * Convert streaming response to our format
   */
  private async *convertStreamToGenerateContentResponse(
    stream: AsyncIterable<BaseMessage>,
    _request: GenerateContentParameters
  ): AsyncGenerator<GenerateContentResponse> {
    let accumulatedContent = '';
    
    for await (const chunk of stream) {
      const chunkText = typeof chunk.content === 'string' ? chunk.content : String(chunk.content);
      accumulatedContent += chunkText;
      
      const content: Content = {
        role: 'model',
        parts: [{ text: accumulatedContent }]
      };

      yield {
        candidates: [{
          content,
          finishReason: 'STOP',
          index: 0
        }],
        usageMetadata: {
          promptTokenCount: 0,
          candidatesTokenCount: 0,
          totalTokenCount: 0
        }
      };
    }
  }

  /**
   * Update the chat model
   */
  updateChatModel(newChatModel: BaseChatModel): void {
    this.chatModel = newChatModel;
  }

  /**
   * Update the embeddings model
   */
  updateEmbeddings(newEmbeddings: Embeddings): void {
    this.embeddings = newEmbeddings;
  }

  /**
   * Update the tools
   */
  updateTools(newTools: Tool[]): void {
    this.tools = newTools;
  }

  /**
   * Get model information
   */
  getModelInfo(): { 
    chatModel: string; 
    embeddings: string; 
    toolCount: number; 
  } {
    return {
      chatModel: this.chatModel.constructor.name,
      embeddings: this.embeddings.constructor.name,
      toolCount: this.tools.length
    };
  }
}