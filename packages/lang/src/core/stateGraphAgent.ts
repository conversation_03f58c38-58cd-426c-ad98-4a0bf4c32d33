/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { Tool } from '@langchain/core/tools';
import {
  AIMessage,
  AIMessageChunk,
  HumanMessage,
  SystemMessage,
  BaseMessage,
  ToolMessage,
  isAIMessageChunk,
} from '@langchain/core/messages';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { CallbackHandlerMethods } from '@langchain/core/callbacks/base';
import logger from '../utils/logger.js';
import { ToolResultProcessor } from '../utils/toolResultProcessor.js';

// Import LangGraph components
import { StateGraph, Annotation } from '@langchain/langgraph';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { MemorySaver } from '@langchain/langgraph';
import type { BaseCheckpointSaver } from '@langchain/langgraph';

import type { LangChainConfig } from '../config/config.js';
import { BASE_PROMPT } from './prompt.js';

// Define proper types for tool calls
interface ToolCallChunk {
  id?: string;
  name?: string;
  args?: string | Record<string, unknown>;
}

interface ToolCallComplete {
  id?: string;
  name: string;
  args: string | Record<string, unknown>;
}

// Helper function to convert to LangChain ToolCall format
function convertToLangChainToolCall(toolCall: ToolCallComplete): Record<string, unknown> {
  return {
    ...toolCall,
    args: typeof toolCall.args === 'string' ? JSON.parse(toolCall.args) : toolCall.args || {},
  };
}

const MAX_TOOL_RESULT_LOG_LENGTH = 500;

/**
 * Simplified state definition for the LangGraph agent
 * Following official LangGraph.js best practices
 */
const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (current: BaseMessage[], update: BaseMessage[]) => current.concat(update),
    default: () => [],
  }),
  userMemory: Annotation<string>({
    reducer: (current: string, update: string) => update || current,
    default: () => "",
  }),
  iterationCount: Annotation<number>({
    reducer: (_current: number, update: number) => update,
    default: () => 0,
  }),
});

type AgentStateType = typeof AgentState.State;
type AgentUpdateType = typeof AgentState.Update;
type CompiledGraphType = ReturnType<StateGraph<typeof AgentState>['compile']>;

// Type-safe node names
const NODE_NAMES = {
  AGENT: "agent",
  TOOLS: "tools",
} as const;

/**
 * LangGraph-based agent using StateGraph
 */
export class StateGraphAgent {
  private chatModel: BaseChatModel;
  private systemPrompt?: string;
  private config: LangChainConfig;
  private checkpointer?: BaseCheckpointSaver;
  private graph: CompiledGraphType;
  private maxIterations: number = 25;
  private callbacks?: CallbackHandlerMethods[];

  constructor(
    config: LangChainConfig,
    options: {
      checkpointer?: BaseCheckpointSaver;
      enablePersistence?: boolean;
      maxIterations?: number;
      callbacks?: CallbackHandlerMethods[];
      systemPrompt?: string,
    } = {}
  ) {
    this.config = config;
    this.chatModel = config.chatModel;
    this.systemPrompt = options.systemPrompt;
    this.maxIterations = options.maxIterations || 25;
    this.callbacks = options.callbacks;

    // Set up checkpointer for persistence
    if (options.enablePersistence !== false) {
      this.checkpointer = options.checkpointer || new MemorySaver();
    }

    this.graph = this.buildGraph();
  }



  /**
   * Get the current tools from config (always up-to-date)
   */
  private getTools(): Tool[] {
    return this.config.getTools();
  }

  /**
   * Create a debug wrapper for the tool node
   * Simplified version with proper type handling
   */
  private createDebugToolWrapper(toolNode: ToolNode<Tool[]>) {
    return async (state: AgentStateType): Promise<AgentUpdateType> => {
      const messages = state.messages;
      const lastMessage = messages[messages.length - 1];

      // Extract tool calls from the last AI message
      if ((lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) && lastMessage.tool_calls?.length) {
        if (this.config.getDebugMode()) {
          logger.debug(`[工具调用] 🔧 StateGraph执行工具: ${lastMessage.tool_calls.map(tc => tc.name).join(', ')}`);
          for (const toolCall of lastMessage.tool_calls) {
            logger.debug(`[工具调用] 📥 工具 ${toolCall.name} 输入参数: ${JSON.stringify(toolCall.args, null, 2)}`);
          }
        }
      }

      // Execute the original tool node - suppress type checking for LangGraph API compatibility
      // @ts-expect-error - ToolNode.invoke expects different state type but works at runtime
      const result = await toolNode.invoke(state);

      // Log tool results in debug mode
      if (this.config.getDebugMode() && result && 'messages' in result && Array.isArray(result.messages)) {
        for (const message of result.messages) {
          if (message instanceof ToolMessage) {
            const toolMessage = message;
            logger.debug(`[工具调用] ✅ 工具执行完成`);
            logger.debug(`[工具调用] 📤 输出结果: ${typeof toolMessage.content === 'string' ? toolMessage.content.substring(0, MAX_TOOL_RESULT_LOG_LENGTH) + (toolMessage.content.length > MAX_TOOL_RESULT_LOG_LENGTH ? '...(省略)' : '') : JSON.stringify(toolMessage.content).substring(0, MAX_TOOL_RESULT_LOG_LENGTH)}`);
          }
        }
      }

      return result as AgentUpdateType;
    };
  }

  /**
   * Build the StateGraph with nodes and edges
   * Simplified version following LangGraph.js best practices
   */
  private buildGraph(): CompiledGraphType {
    const workflow = new StateGraph(AgentState);

    // Add the agent node (calls the LLM)
    workflow.addNode(NODE_NAMES.AGENT, this.callModel.bind(this));

    // Add tool node using LangGraph's built-in ToolNode with debug wrapper
    const toolNode = new ToolNode(this.getTools());
    workflow.addNode(NODE_NAMES.TOOLS, this.createDebugToolWrapper(toolNode));

    // Set entry point using addEdge with __start__
    (workflow as unknown as { addEdge: (from: string, to: string) => void }).addEdge("__start__", NODE_NAMES.AGENT);

    // Add conditional edges from agent
    (workflow as unknown as { addConditionalEdges: (from: string, fn: (s: AgentStateType) => "continue" | "end", mapping: Record<string, string>) => void }).addConditionalEdges(
      NODE_NAMES.AGENT,
      this.shouldContinue.bind(this),
      {
        continue: NODE_NAMES.TOOLS,
        end: "__end__",
      }
    );

    // Add edge from tools back to agent
    (workflow as unknown as { addEdge: (from: string, to: string) => void }).addEdge(NODE_NAMES.TOOLS, NODE_NAMES.AGENT);

    // Compile the graph with checkpointer for persistence
    const compileOptions: { checkpointer?: BaseCheckpointSaver } = {};
    if (this.checkpointer) {
      compileOptions.checkpointer = this.checkpointer;
    }

    // @ts-expect-error - Return type is complex but runtime-safe
    return workflow.compile(compileOptions);
  }

  /**
   * Check if messages contain tool-related content that might not be supported
   */
  private hasToolMessages(messages: BaseMessage[]): boolean {
    return messages.some(msg => 
      msg instanceof ToolMessage || 
      (msg instanceof AIMessage && msg.tool_calls && msg.tool_calls.length > 0)
    );
  }

  /**
   * The agent node - calls the LLM with the current state
   * Optimized with smart fallback to avoid retry on tool message errors
   */
  private async callModel(state: AgentStateType): Promise<AgentUpdateType> {
    logger.debug('[StateGraphAgent] Calling model with', state.messages.length, 'messages');

    // Prepare messages with system prompt
    const messages = this.prepareMessages(state.messages, state.userMemory);

    // Check if we should use fallback mode immediately for better compatibility
    const hasToolContent = this.hasToolMessages(messages);
    const shouldUseFallback = hasToolContent && this.shouldPreferFallback();

    // Bind tools to the model if available and not using fallback
    let modelWithTools: BaseChatModel = this.chatModel;
    const tools = this.getTools();
    if (tools.length > 0 && this.chatModel.bindTools && !shouldUseFallback) {
      try {
        modelWithTools = this.chatModel.bindTools(tools) as BaseChatModel;
        logger.debug('[StateGraphAgent] Tools successfully bound to model');
      } catch (error) {
        logger.warning('[StateGraphAgent] Tool binding failed, proceeding without tools:', error);
      }
    }

    // Choose messages based on fallback decision
    const finalMessages = shouldUseFallback 
      ? ToolResultProcessor.stripToolMessagesForFallback(messages)
      : messages;

    const modelToUse = shouldUseFallback ? this.chatModel : modelWithTools;

    if (shouldUseFallback) {
      logger.debug('[StateGraphAgent] Using fallback mode for better compatibility');
    }

    try {
      // Generate response - use invoke for agent node (streaming is handled at the graph level)
      logger.debug('[StateGraphAgent] Invoking model with', finalMessages.length, 'messages');
      const response = await modelToUse.invoke(finalMessages, this.callbacks ? { callbacks: this.callbacks } : undefined);
      logger.debug('[StateGraphAgent] Model response received:', response?.constructor?.name, response?.content ? 'with content' : 'without content');

      // Check if response is empty
      if (!response) {
        logger.error('[StateGraphAgent] Model returned null/undefined response');
        throw new Error('Received null response from chat model');
      }

      // Check if response is truly empty (no content AND no tool calls)
      const hasContent = response.content && (typeof response.content === 'string' ? response.content.trim() !== '' : true);
      const hasToolCalls = (response instanceof AIMessage || response instanceof AIMessageChunk) &&
                          response.tool_calls && response.tool_calls.length > 0;

      if (!hasContent && !hasToolCalls) {
        logger.error('[StateGraphAgent] Model returned empty content and no tool calls');
        throw new Error('Received empty response from chat model call');
      }

      // Increment iteration count
      const newIterationCount = state.iterationCount + 1;

      // Check if we've exceeded max iterations
      if (newIterationCount >= this.maxIterations) {
        logger.warning(`[StateGraphAgent] Maximum iterations (${this.maxIterations}) reached`);

        // Force end by creating a non-tool response
        const finalResponse = new AIMessage({
          content: response instanceof AIMessage && typeof response.content === 'string'
            ? response.content
            : "I've reached the maximum number of iterations. Let me know if you need further assistance.",
        });

        return {
          messages: [finalResponse],
          iterationCount: newIterationCount,
        };
      }

      return {
        messages: [response],
        iterationCount: newIterationCount,
      };
    } catch (error) {
      // Only fallback if we haven't already tried fallback mode
      if (!shouldUseFallback && hasToolContent) {
        logger.warning('[StateGraphAgent] Model call failed, retrying with fallback mode:', error);
        try {
          const fallbackMessages = ToolResultProcessor.stripToolMessagesForFallback(messages);
          const fallbackResponse = await this.chatModel.invoke(fallbackMessages, this.callbacks ? { callbacks: this.callbacks } : undefined);
          const newIterationCount = state.iterationCount + 1;
          return { messages: [fallbackResponse], iterationCount: newIterationCount };
        } catch (fallbackError) {
          logger.error('[StateGraphAgent] Fallback model call also failed:', fallbackError);
          throw fallbackError;
        }
      } else {
        logger.error('[StateGraphAgent] Model call failed:', error);
        throw error;
      }
    }
  }

  /**
   * Determine if we should prefer fallback mode based on model type or previous failures
   */
  private shouldPreferFallback(): boolean {
    // For now, use conservative approach for OpenAI-compatible APIs
    // This can be enhanced with more sophisticated detection logic
    const modelName = this.chatModel.constructor.name.toLowerCase();
    return modelName.includes('openai') && !modelName.includes('gpt');
  }

  /**
   * Determines whether to continue to tools or end
   * Simplified logic following LangGraph.js best practices
   */
  private shouldContinue(state: AgentStateType): "continue" | "end" {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    // Check iteration limit
    if (state.iterationCount >= this.maxIterations) {
      logger.debug('[StateGraphAgent] Max iterations reached, ending');
      return "end";
    }

    // If the last message has tool calls, continue to tools
    if ((lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) && lastMessage.tool_calls?.length) {
      logger.debug('[StateGraphAgent] Tool calls detected, continuing to tools');
      return "continue";
    }

    // Otherwise, end the conversation
    logger.debug('[StateGraphAgent] No tool calls, ending conversation');
    return "end";
  }

  /**
   * Prepare messages for the chat model, including system prompt
   * Simplified version following LangGraph.js best practices
   */
  private prepareMessages(messages: BaseMessage[], userMemory?: string): BaseMessage[] {
    const preparedMessages: BaseMessage[] = [];

    // Use the core system prompt that mirrors the core package functionality
    let prompt = this.systemPrompt || this.getCoreSystemPrompt(userMemory);

    // Add tool information to system prompt if tools are available
    const tools = this.getTools();
    if (tools.length > 0) {
      prompt += `\n\n# Available Tools\n\nYou have access to the following tools:\n`;
      for (const tool of tools) {
        prompt += `\n## ${tool.name}\n`;
        prompt += `**Description**: ${tool.description}\n`;
      }
      prompt += `\nUse these tools when needed to help the user. Always call the appropriate tool to gather information before making changes or providing answers.`;
    }

    preparedMessages.push(new SystemMessage(prompt));

    // Sanitize conversation messages to ensure all contents are valid strings
    const sanitizedMessages: BaseMessage[] = ToolResultProcessor.sanitizeMessages(messages);

    // Add sanitized conversation messages
    preparedMessages.push(...sanitizedMessages);

    // Final cleanup: remove any empty SystemMessages that might have been created during processing
    const filteredMessages = preparedMessages.filter((message, index) => {
      if (message instanceof SystemMessage) {
        const content = typeof message.content === 'string' ? message.content : '';
        const hasValidContent = content.trim().length > 0;
        if (!hasValidContent && index > 0) { // Keep the first SystemMessage even if empty (main prompt)
          logger.debug(`[StateGraphAgent] Filtering out empty SystemMessage at index ${index}`);
          return false;
        }
      }
      return true;
    });

    // Debug mode: print complete LLM input
    // if (this.config.getDebugMode()) {
    //   logger.debug('\n=== LLM 完整输入 ===');
    //   filteredMessages.forEach((message, index) => {
    //     const messageType = message.constructor.name;
    //     const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
    //     logger.debug(`\n[${index}] ${messageType}:`);
    //     logger.debug(content);
    //     logger.debug('---');
    //   });
    //   logger.debug('=== LLM 输入结束 ===\n');
    // }

    return filteredMessages;
  }

  /**
   * Process a user message using the StateGraph
   * Simplified version following LangGraph.js best practices
   */
  async processMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): Promise<string> {
    logger.debug('[StateGraphAgent] Processing message:', userMessage);

    // Validate input parameters
    if (!userMessage?.trim()) {
      throw new Error('Invalid user message: must be a non-empty string');
    }

    if (!sessionId?.trim()) {
      throw new Error('Invalid session ID: must be a non-empty string');
    }

    // Create initial state - LangGraph MemorySaver will merge with existing thread state
    // When conversationHistory is empty, we rely on MemorySaver to load previous messages
    const initialState = {
      messages: conversationHistory.length > 0 
        ? [...conversationHistory, new HumanMessage(userMessage)]
        : [new HumanMessage(userMessage)], // Let MemorySaver handle history loading
      userMemory: userMemory || "",
      iterationCount: 0,
    };

    // Run the graph with proper configuration including thread_id
    const config = {
      recursionLimit: this.maxIterations,
      configurable: {
        thread_id: sessionId, // This is required for MemorySaver
      },
    };

    const result = await this.graph.invoke(initialState, this.callbacks ? { ...config, callbacks: this.callbacks } : config);

    // Extract the final response
    const finalMessages = result.messages;
    if (!finalMessages || finalMessages.length === 0) {
      logger.warning('[StateGraphAgent] No messages in result');
      return 'No response generated';
    }

    const lastMessage = finalMessages[finalMessages.length - 1];

    if (lastMessage instanceof AIMessage || lastMessage instanceof AIMessageChunk) {
      if (typeof lastMessage.content === 'string') {
        return lastMessage.content;
      } else if (lastMessage.content && typeof lastMessage.content === 'object') {
        return JSON.stringify(lastMessage.content);
      } else {
        return 'Empty response from model';
      }
    }

    logger.warning('[StateGraphAgent] Last message is not an AIMessage or AIMessageChunk:', lastMessage?.constructor?.name);
    return 'No valid response generated';
  }

  async *messageStream(message: AIMessage | AIMessageChunk) {
    const safeContent = ToolResultProcessor.toSafeString(message.content);
    if (ToolResultProcessor.isValidContent(safeContent)) {
      yield safeContent;
    }
  }

  /**
   * Intelligent streaming message handler with optimal user experience
   * Uses direct model streaming for simple queries and LangGraph for tool-requiring tasks
   */
  async *streamMessage(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    logger.debug('[StateGraphAgent] Intelligent streaming for:', userMessage);

    // Validate input parameters
    if (!userMessage?.trim()) {
      throw new Error('Invalid user message: must be a non-empty string');
    }

    if (!sessionId?.trim()) {
      throw new Error('Invalid session ID: must be a non-empty string');
    }

    try {
      // Smart decision: Check if tools are likely needed
      // const likelyNeedsTools = this.detectToolRequirement(userMessage);
      yield* this.optimizedDirectStream(userMessage, sessionId, userMemory, conversationHistory);

    } catch (error) {
      logger.error('[StateGraphAgent] Intelligent streaming failed:', error);
      // Ultimate fallback
      yield `Error: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Unified streaming handler supporting both simple responses and tool calls
   * Handles different chunk types for comprehensive streaming experience
   */
  private async *optimizedDirectStream(
    userMessage: string,
    sessionId: string,
    userMemory?: string,
    conversationHistory: BaseMessage[] = []
  ): AsyncGenerator<string> {
    try {
      // Prepare messages for streaming
      const messages = this.prepareMessages([...conversationHistory, new HumanMessage(userMessage)], userMemory);
      
      // Get tools and bind them to the model for tool-aware streaming
      const tools = this.getTools();
      let modelWithTools = this.chatModel;
      
      if (tools.length > 0 && this.chatModel.bindTools) {
        try {
          modelWithTools = this.chatModel.bindTools(tools) as BaseChatModel;
          logger.debug('[OptimizedStream] Tools bound to model for streaming');
        } catch (error) {
          logger.warning('[OptimizedStream] Tool binding failed, proceeding without tools:', error);
        }
      }
      
      // Stream from model with tool support
      const stream = await modelWithTools.stream(messages, this.callbacks ? { callbacks: this.callbacks } : undefined);
      
      let hasToolCalls = false;
      const accumulatedToolCalls: ToolCallChunk[] = [];
      let aiMessageWithToolCalls: AIMessage | null = null;
      
      for await (const chunk of stream) {
        // Handle AIMessageChunk with content
        if (isAIMessageChunk(chunk)) {
          // Handle text content streaming
          const content = this.extractMessageContent(chunk);
          if (content) {
            yield content;
          }
          
          // Handle tool call chunks
          if (chunk.tool_call_chunks?.length) {
            hasToolCalls = true;
            const toolCallChunks = chunk.tool_call_chunks as ToolCallChunk[];
            yield* this.handleToolCallChunks(toolCallChunks, accumulatedToolCalls);
          }
          
          // Handle complete tool calls
          if (chunk.tool_calls?.length) {
            hasToolCalls = true;
            // Create AIMessage with tool calls for conversation history
            const toolCalls = chunk.tool_calls as ToolCallComplete[];
            aiMessageWithToolCalls = new AIMessage({
              content: chunk.content || '',
              tool_calls: toolCalls.map(convertToLangChainToolCall) as any,
            });
            yield* this.handleToolCalls(toolCalls, sessionId, [...conversationHistory, aiMessageWithToolCalls], userMessage);
          }
        }
        // Handle other chunk types (cast to any for broader compatibility)
        else if (chunk && typeof chunk === 'object') {
          const anyChunk = chunk as AIMessage | AIMessageChunk;
          
          // Check if it's an AIMessage-like object
          if (anyChunk.constructor?.name === 'AIMessage' || anyChunk._getType?.() === 'ai') {
            const content = this.extractMessageContent(anyChunk);
            if (content) {
              yield content;
            }
            
            if ((anyChunk as AIMessage).tool_calls?.length) {
              hasToolCalls = true;
              // Create AIMessage with tool calls for conversation history  
              const toolCalls = (anyChunk as AIMessage).tool_calls as ToolCallComplete[];
              aiMessageWithToolCalls = new AIMessage({
                content: anyChunk.content || '',
                tool_calls: toolCalls.map(convertToLangChainToolCall) as any,
              });
              yield* this.handleToolCalls(toolCalls, sessionId, [...conversationHistory, aiMessageWithToolCalls], userMessage);
            }
          }
          // Handle any other chunk types with content
          else if ('content' in anyChunk) {
            const content = this.extractMessageContent(anyChunk);
            if (content) {
              yield content;
            }
          }
        }
      }
      
      // If we had tool calls, ensure they are properly processed
      if (hasToolCalls && accumulatedToolCalls.length > 0) {
        // Create AIMessage for accumulated tool calls
        if (!aiMessageWithToolCalls) {
          const validToolCalls = accumulatedToolCalls.filter(tc => tc.name && tc.args) as ToolCallComplete[];
          aiMessageWithToolCalls = new AIMessage({
            content: '',
            tool_calls: validToolCalls.map(convertToLangChainToolCall) as any,
          });
        }
        yield* this.executeAccumulatedToolCalls(accumulatedToolCalls, sessionId, [...conversationHistory, aiMessageWithToolCalls], userMessage);
      }
      
    } catch (error) {
      logger.error('[OptimizedStream] Unified streaming failed:', error);
      // Ultimate fallback to non-streaming
      const result = await this.processMessage(userMessage, sessionId, userMemory, conversationHistory);
      yield result;
    }
  }

  /**
   * Handle tool call chunks for streaming tool call information
   */
  private async *handleToolCallChunks(toolCallChunks: ToolCallChunk[], accumulatedToolCalls: ToolCallChunk[]): AsyncGenerator<string> {
    for (const chunk of toolCallChunks) {
      if (chunk.name) {
        yield `\n\n🔧 调用工具: ${chunk.name}`;
        if (chunk.args) {
          try {
            const args = typeof chunk.args === 'string' ? JSON.parse(chunk.args) : chunk.args;
            yield `\n📥 参数: ${JSON.stringify(args, null, 2)}`;
          } catch {
            yield `\n📥 参数: ${chunk.args}`;
          }
        }
      }
      
      // Accumulate tool call chunks for later execution
      this.accumulateToolCallChunk(chunk, accumulatedToolCalls);
    }
  }

  /**
   * Handle complete tool calls and continue with model response
   */
  private async *handleToolCalls(
    toolCalls: ToolCallComplete[], 
    sessionId: string, 
    conversationHistory: BaseMessage[],
    userMessage?: string
  ): AsyncGenerator<string> {
    // Collect all tool results first
    const toolMessages: ToolMessage[] = [];
    
    for (const toolCall of toolCalls) {
      yield `\n\n🔧 执行工具: ${toolCall.name}`;
      
      try {
        let args = typeof toolCall.args === 'string' ? JSON.parse(toolCall.args) : toolCall.args;
        
        // Handle undefined or null args
        if (!args) {
          args = {};
        }
        
        // Handle special cases for tools that need default parameters
        if (toolCall.name === 'list_directory' && (!args || Object.keys(args).length === 0)) {
          args = { path: process.cwd() }; // Default to current directory with absolute path
        } else if (toolCall.name === 'get_current_time' && (!args || Object.keys(args).length === 0)) {
          args = { timezone: 'Asia/Shanghai' }; // Default timezone
        }
        
        yield `\n📥 输入: ${JSON.stringify(args, null, 2)}`;
        
        // Execute the tool with processed args
        const toolCallWithArgs = { ...toolCall, args };
        const toolResult = await this.executeToolCall(toolCallWithArgs);
        yield `\n📤 输出: ${toolResult.substring(0, 500)}${toolResult.length > 500 ? '...(省略)' : ''}`;
        
        // Create tool message for conversation history
        toolMessages.push(new ToolMessage({
          content: toolResult,
          tool_call_id: toolCall.id || `tool_${Date.now()}_${Math.random()}`,
        }));
        
      } catch (error) {
        const errorMessage = `工具执行失败: ${error instanceof Error ? error.message : String(error)}`;
        yield `\n❌ ${errorMessage}`;
        
        // Create error tool message
        toolMessages.push(new ToolMessage({
          content: errorMessage,
          tool_call_id: toolCall.id || `tool_${Date.now()}_${Math.random()}`,
        }));
      }
    }
    
    // Continue with model response after all tools are executed
    yield `\n\n💭 分析结果...\n`;
    
    // Generate follow-up response based on tool results
    yield* this.generateFollowUpResponse(toolMessages, conversationHistory, userMessage || '', sessionId);
  }

  /**
   * Accumulate tool call chunks
   */
  private accumulateToolCallChunk(chunk: ToolCallChunk, accumulated: ToolCallChunk[]): void {
    // Find existing tool call or create new one
    let existingCall = accumulated.find(call => call.id === chunk.id);
    if (!existingCall) {
      existingCall = { id: chunk.id, name: '', args: '' };
      accumulated.push(existingCall);
    }
    
    // Accumulate name and args
    if (chunk.name) {
      existingCall.name = (existingCall.name || '') + chunk.name;
    }
    if (chunk.args) {
      const currentArgs = existingCall.args || '';
      const newArgs = chunk.args;
      existingCall.args = (typeof currentArgs === 'string' ? currentArgs : '') + (typeof newArgs === 'string' ? newArgs : '');
    }
  }

  /**
   * Execute accumulated tool calls and continue with model response
   */
  private async *executeAccumulatedToolCalls(
    toolCalls: ToolCallChunk[], 
    sessionId: string, 
    conversationHistory: BaseMessage[],
    userMessage?: string
  ): AsyncGenerator<string> {
    const toolMessages: ToolMessage[] = [];
    
    for (const toolCall of toolCalls) {
      if (toolCall.name && toolCall.args) {
        try {
          const toolResult = await this.executeToolCall({
            name: toolCall.name,
            args: toolCall.args
          });
          yield `\n\n✅ 工具 ${toolCall.name} 执行完成`;
          yield `\n📤 结果: ${toolResult.substring(0, 500)}${toolResult.length > 500 ? '...(省略)' : ''}`;
          
          // Create tool message for conversation history
          toolMessages.push(new ToolMessage({
            content: toolResult,
            tool_call_id: toolCall.id || `tool_${Date.now()}_${Math.random()}`,
          }));
        } catch (error) {
          const errorMessage = `工具执行失败: ${error instanceof Error ? error.message : String(error)}`;
          yield `\n❌ 工具 ${toolCall.name} ${errorMessage}`;
          
          // Create error tool message
          toolMessages.push(new ToolMessage({
            content: errorMessage,
            tool_call_id: toolCall.id || `tool_${Date.now()}_${Math.random()}`,
          }));
        }
      }
    }
    
    // Continue with model response after all tools are executed
    if (toolMessages.length > 0) {
      yield `\n\n💭 分析结果...`;
      yield* this.generateFollowUpResponse(toolMessages, conversationHistory, userMessage || '', sessionId);
    }
  }

  /**
   * Execute a single tool call
   */
  private async executeToolCall(toolCall: ToolCallComplete | ToolCallChunk): Promise<string> {
    const tools = this.getTools();
    const tool = tools.find(t => t.name === toolCall.name);
    
    if (!tool) {
      throw new Error(`Tool ${toolCall.name} not found`);
    }
    
    let args = typeof toolCall.args === 'string' ? JSON.parse(toolCall.args) : toolCall.args;
    
    // Handle special cases for tools that need default parameters
    if (toolCall.name === 'list_directory' && (!args || Object.keys(args).length === 0)) {
      args = { path: process.cwd() }; // Default to current directory with absolute path
    } else if (toolCall.name === 'get_current_time' && (!args || Object.keys(args).length === 0)) {
      args = { timezone: 'Asia/Shanghai' }; // Default timezone
    }
    
    // CoreToolWrapper expects { args: {...} } format
    const toolInput = { args };
    const result = await tool.invoke(toolInput);
    
    return typeof result === 'string' ? result : JSON.stringify(result);
  }

  /**
   * Extract content from AI message or chunk
   */
  private extractMessageContent(message: AIMessage | AIMessageChunk): string {
    if (typeof message.content === 'string') {
      return message.content;
    } else if (message.content && typeof message.content === 'object') {
      // Handle structured content
      if (Array.isArray(message.content)) {
        return message.content
          .map(item => {
            if (typeof item === 'string') {
              return item;
            } else if (item && typeof item === 'object') {
              // Handle different content types safely
              if ('text' in item && typeof item.text === 'string') {
                return item.text;
              } else if ('content' in item && typeof item.content === 'string') {
                return item.content;
              }
            }
            return '';
          })
          .join('');
      }
    }
    return '';
  }

  /**
   * Get the core system prompt that mirrors packages/core/src/core/prompts.ts
   */
  private getCoreSystemPrompt(userMemory?: string): string {

    const currentDir = process.cwd();
    const os = process.platform;
    const userInfo = `
    <user_info>
      The user's OS version is ${os}. The absolute path of the user's workspace is ${currentDir}. The user's shell is ${process.env.SHELL}.
    </user_info>
    `.trim();

    const memorySuffix =
      userMemory && userMemory.trim().length > 0
        ? `\n\n---\n\n${userMemory.trim()}`
        : '';

    return `${BASE_PROMPT}${userInfo}${memorySuffix}`;
  }

  /**
   * Update the system prompt
   */
  updateSystemPrompt(prompt: string): void {
    this.systemPrompt = prompt;
    // Rebuild graph with new prompt
    this.graph = this.buildGraph();
  }

  /**
   * Update the model configuration
   */
  updateConfig(config: LangChainConfig): void {
    this.config = config;
    this.chatModel = config.chatModel;
    // Rebuild graph with new configuration
    this.graph = this.buildGraph();
  }

  /**
   * Get current configuration info
   */
  getConfigInfo(): {
    modelName: string;
    toolCount: number;
    hasSystemPrompt: boolean;
    maxIterations: number;
  } {
    const tools = this.getTools();
    return {
      modelName: this.chatModel.constructor.name,
      toolCount: tools.length,
      hasSystemPrompt: !!this.systemPrompt,
      maxIterations: this.maxIterations,
    };
  }

  /**
   * Generate follow-up response based on tool results
   */
  private async *generateFollowUpResponse(
    toolMessages: ToolMessage[], 
    conversationHistory: BaseMessage[],
    userMessage: string,
    sessionId: string
  ): AsyncGenerator<string> {
    try {
      // Try to get complete conversation history from LangGraph state if available
      let completeHistory: BaseMessage[] = [];
      
      try {
        const currentState = await this.graph.getState({ configurable: { thread_id: sessionId } });
        if (currentState && currentState.values && currentState.values.messages) {
          completeHistory = [...currentState.values.messages];
          logger.debug('[StateGraphAgent] Retrieved conversation history from LangGraph state');
        }
      } catch (stateError) {
        logger.debug('[StateGraphAgent] Could not retrieve state, using provided history:', stateError);
        completeHistory = [...conversationHistory];
      }
      
      // If we still don't have history, create minimal context
      if (completeHistory.length === 0) {
        const { HumanMessage } = await import('@langchain/core/messages');
        completeHistory = [new HumanMessage(userMessage)];
        logger.debug('[StateGraphAgent] Created minimal conversation context');
      }
      
      // Add tool results to the conversation history
      for (const toolMessage of toolMessages) {
        completeHistory.push(toolMessage);
      }
      
      // Add a prompt to analyze the tool results
      const { HumanMessage } = await import('@langchain/core/messages');
      completeHistory.push(new HumanMessage('请根据上述工具执行结果，提供详细的分析和回复。'));
      
      // Prepare messages for final model call
      const finalMessages = this.prepareMessages(completeHistory);
      
      // Stream the final response from model
      const stream = await this.chatModel.stream(finalMessages, this.callbacks ? { callbacks: this.callbacks } : undefined);
      
      for await (const chunk of stream) {
        if (isAIMessageChunk(chunk)) {
          const content = this.extractMessageContent(chunk);
          if (content) {
            yield content;
          }
        }
      }
      
    } catch (error) {
      logger.error('[StateGraphAgent] Follow-up response generation failed:', error);
      yield `\n抱歉，在分析工具结果时出现了错误: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Get the compiled graph for advanced operations
   */
  getGraph() {
    return this.graph;
  }
}
