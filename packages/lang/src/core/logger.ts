/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'node:path';
import { promises as fs } from 'node:fs';
import { BaseMessage } from '@langchain/core/messages';
import { getProjectTempDir } from '../utils/paths.js';
import { logger as log } from '../utils/logger.js';
import { SESSIONS_DIR_NAME, SESSION_FILE_NAME } from '../config/constants.js';

export enum MessageSenderType {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export interface LogEntry {
  sessionId: string;
  messageId: number;
  timestamp: string;
  type: MessageSenderType;
  message: string;
  metadata?: Record<string, unknown>;
}

export class HaicodeLogger {
  private haicodeDir: string | undefined;
  private sessionDir: string | undefined;
  private sessionMetaFilePath: string | undefined;
  private sessionId: string | undefined;
  private messageId = 0; // Instance-specific counter for the next messageId
  private initialized = false;
  private logs: LogEntry[] = []; // In-memory cache for current session logs

  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }



  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.haicodeDir = getProjectTempDir(process.cwd());
    const sessionsDir = path.join(this.haicodeDir, SESSIONS_DIR_NAME);
    this.sessionDir = path.join(sessionsDir, this.sessionId!);
    this.sessionMetaFilePath = path.join(this.sessionDir, SESSION_FILE_NAME);

    try {
      // Create session directory structure
      await fs.mkdir(this.sessionDir, { recursive: true });

      // 移除 logs.json 文件的读取和创建逻辑，仅初始化内存状态
      this.messageId = 0;
      this.logs = [];

      this.initialized = true;
      log.debug(`[HaicodeLogger] Initialized logger for session ${this.sessionId}`);
    } catch (err) {
      log.error('Failed to initialize haicode logger:', err);
      this.initialized = false;
    }
  }

  private async _updateSessionLogFile(
    entryToAppend: LogEntry,
  ): Promise<LogEntry | null> {
    try {
      // 仅在内存中更新日志，不写入文件
      const finalEntryToAppend: LogEntry = {
        ...entryToAppend,
        messageId: this.messageId,
      };

      this.logs.push(finalEntryToAppend);
      this.messageId++;

      log.debug(`[HaicodeLogger] Added log entry to memory for session ${this.sessionId}`);
      return finalEntryToAppend;
    } catch (error) {
      log.debug('Error updating session log in memory:', error);
      throw error;
    }
  }

  async log(message: string, type: MessageSenderType = MessageSenderType.USER, metadata?: Record<string, unknown>): Promise<void> {
    if (!this.initialized) {
      log.debug(
        'Haicode logger not initialized. Cannot log message.',
      );
      return;
    }

    const entry: LogEntry = {
      sessionId: this.sessionId!,
      messageId: this.messageId,
      timestamp: new Date().toISOString(),
      type,
      message,
      metadata,
    };

    try {
      await this._updateSessionLogFile(entry);
    } catch (error) {
      log.error('Failed to log message:', error);
    }
  }

  private _checkpointPath(tag: string): string {
    if (!tag.length) {
      throw new Error('No checkpoint tag specified.');
    }
    if (!this.sessionDir) {
      throw new Error('Session directory path not set.');
    }
    // Sanitize tag to prevent directory traversal attacks (same as core Logger)
    tag = tag.replace(/[^a-zA-Z0-9-_]/g, '');
    if (!tag) {
      log.error('Sanitized tag is empty setting to "default".');
      tag = 'default';
    }
    return path.join(this.sessionDir, `checkpoint-${tag}.json`);
  }

  async saveCheckpoint(conversation: BaseMessage[], tag: string): Promise<void> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot save a checkpoint.',
      );
      return;
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      // Convert BaseMessage to serializable format compatible with core logger
      const serializedConversation = conversation.map(msg => ({
        role: msg.constructor.name === 'HumanMessage' ? 'user' : 'model',
        parts: [{ text: String(msg.content) }],
        // Keep additional data for compatibility
        _langchain_type: msg.constructor.name,
        _additional_kwargs: msg.additional_kwargs,
      }));
      await fs.writeFile(checkpointPath, JSON.stringify(serializedConversation, null, 2), 'utf-8');
      log.debug(`Saved checkpoint '${tag}' to ${checkpointPath}`);
    } catch (error) {
      log.error('Error writing to checkpoint file:', error);
    }
  }

  async loadCheckpoint(tag: string): Promise<BaseMessage[]> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot load checkpoint.',
      );
      return [];
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      const fileContent = await fs.readFile(checkpointPath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);
      if (!Array.isArray(parsedContent)) {
        log.warning(
          `Checkpoint file at ${checkpointPath} is not a valid JSON array. Returning empty checkpoint.`,
        );
        return [];
      }
      
      // Convert back from core logger format to LangChain BaseMessage
      const { HumanMessage, AIMessage, ToolMessage } = await import('@langchain/core/messages');

      return parsedContent.map((msgData: Record<string, unknown>) => {
        // Safely extract content from different formats
        const parts = msgData.parts as Array<{ text?: string }> | undefined;
        const content = parts?.[0]?.text || String(msgData.content || '');
        const additionalKwargs = (msgData._additional_kwargs || msgData.additional_kwargs || {}) as Record<string, unknown>;

        // Determine message type based on role or langchain type
        if (msgData.role === 'user' || msgData._langchain_type === 'HumanMessage') {
          return new HumanMessage({ content, additional_kwargs: additionalKwargs });
        } else if (msgData._langchain_type === 'ToolMessage') {
          // Reconstruct ToolMessage
          return new ToolMessage({
            content,
            tool_call_id: String(msgData.tool_call_id || ''),
            additional_kwargs: additionalKwargs,
          });
        } else {
          // AIMessage - reconstruct with tool calls if present
          const aiMessage = new AIMessage({ content, additional_kwargs: additionalKwargs });
          if (msgData.tool_calls && Array.isArray(msgData.tool_calls)) {
            aiMessage.tool_calls = msgData.tool_calls as Array<{
              id: string;
              name: string;
              args: Record<string, unknown>;
            }>;
          }
          return aiMessage;
        }
      });
    } catch (error) {
      log.error(`Failed to read or parse checkpoint file ${checkpointPath}:`, error);
      const nodeError = error as NodeJS.ErrnoException;
      if (nodeError.code === 'ENOENT') {
        // File doesn't exist, which is fine. Return empty array.
        return [];
      }
      return [];
    }
  }

  async saveSessionMetadata(metadata: Record<string, unknown>): Promise<void> {
    if (!this.initialized || !this.sessionMetaFilePath) {
      log.error('Logger not initialized or session metadata file path not set.');
      return;
    }

    try {
      const sessionMetadata = {
        sessionId: this.sessionId,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        ...metadata,
      };

      await fs.writeFile(
        this.sessionMetaFilePath,
        JSON.stringify(sessionMetadata, null, 2),
        'utf-8'
      );
      log.debug(`Saved session metadata to ${this.sessionMetaFilePath}`);
    } catch (error) {
      log.error('Error writing session metadata file:', error);
    }
  }

  getSessionLogs(): LogEntry[] {
    return [...this.logs]; // Return all logs since they're session-specific now
  }

  getAllLogs(): LogEntry[] {
    return [...this.logs]; // Same as getSessionLogs since logs are session-specific
  }
}