/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { BaseMessage, AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type { ConversationTurn } from './turnManager.js';

/**
 * Cache entry for compressed conversations
 */
interface CacheEntry {
  key: string;
  content: string;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

/**
 * Configuration for performance optimization
 */
interface PerformanceConfig {
  maxCacheSize: number; // Maximum cache size in MB
  maxCacheEntries: number; // Maximum number of cache entries
  compressionRatio: number; // Target compression ratio (0.1 = 90% compression)
  tokenLimit: number; // Maximum tokens before compression
  cacheTTL: number; // Cache time-to-live in milliseconds
  enableCompression: boolean;
  enableCaching: boolean;
  compressionStrategy: 'summarization' | 'token-trimming' | 'semantic-compression';
}

/**
 * Performance optimizer for LangGraph conversations
 * Implements session compression and intelligent caching
 */
export class LangChainPerformanceOptimizer {
  private cache: Map<string, CacheEntry> = new Map();
  private config: PerformanceConfig;
  private compressionModel?: BaseChatModel;
  private currentCacheSize: number = 0;

  constructor(
    config: Partial<PerformanceConfig> = {},
    compressionModel?: BaseChatModel
  ) {
    this.config = {
      maxCacheSize: config.maxCacheSize || 100, // 100MB
      maxCacheEntries: config.maxCacheEntries || 1000,
      compressionRatio: config.compressionRatio || 0.3, // 70% compression
      tokenLimit: config.tokenLimit || 4000,
      cacheTTL: config.cacheTTL || 3600000, // 1 hour
      enableCompression: config.enableCompression !== false,
      enableCaching: config.enableCaching !== false,
      compressionStrategy: config.compressionStrategy || 'summarization',
    };
    this.compressionModel = compressionModel;
  }

  /**
   * Compress conversation history using selected strategy
   */
  async compressConversation(
    messages: BaseMessage[],
    sessionId: string,
    targetTokens?: number
  ): Promise<BaseMessage[]> {
    if (!this.config.enableCompression || messages.length === 0) {
      return messages;
    }

    const cacheKey = this.generateCacheKey(messages, 'compression');

    // Check cache first
    if (this.config.enableCaching) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        logger.info(`[PerformanceOptimizer] Using cached compression for session ${sessionId}`);
        return this.parseMessagesFromCache(cached);
      }
    }

    const targetTokenCount = targetTokens || Math.floor(this.estimateTokens(messages) * this.config.compressionRatio);
    let compressedMessages: BaseMessage[];

    switch (this.config.compressionStrategy) {
      case 'summarization':
        compressedMessages = await this.summarizeConversation(messages, targetTokenCount);
        break;
      case 'token-trimming':
        compressedMessages = this.trimByTokens(messages, targetTokenCount);
        break;
      case 'semantic-compression':
        compressedMessages = await this.semanticCompression(messages, targetTokenCount);
        break;
      default:
        compressedMessages = messages;
    }

    // Cache the result
    if (this.config.enableCaching) {
      this.addToCache(cacheKey, this.serializeMessages(compressedMessages));
    }

    logger.info(`[PerformanceOptimizer] Compressed ${messages.length} messages to ${compressedMessages.length} for session ${sessionId}`);

    return compressedMessages;
  }

  /**
   * Compress conversation turns while preserving important context
   */
  async compressTurns(
    turns: ConversationTurn[],
    sessionId: string
  ): Promise<ConversationTurn[]> {
    if (!this.config.enableCompression || turns.length === 0) {
      return turns;
    }

    // Keep recent turns and compress older ones
    const recentTurns = turns.slice(-10); // Keep last 10 turns
    const oldTurns = turns.slice(0, -10);

    if (oldTurns.length === 0) {
      return turns;
    }

    // Extract messages from old turns
    const oldMessages: BaseMessage[] = [];
    for (const turn of oldTurns) {
      oldMessages.push(turn.userMessage);
      if (turn.assistantMessage) {
        oldMessages.push(turn.assistantMessage);
      }
    }

    // Compress old messages
    const compressedMessages = await this.compressConversation(oldMessages, sessionId);

    // Create a compressed turn summary
    const compressedTurn: ConversationTurn = {
      id: `compressed_${sessionId}_${Date.now()}`,
      sessionId,
      timestamp: oldTurns[0]?.timestamp || new Date(),
      userMessage: new HumanMessage({
        content: `[Compressed conversation history: ${oldTurns.length} turns]`,
      }),
      assistantMessage: new AIMessage({
        content: this.extractCompressedContent(compressedMessages),
      }),
      metadata: {
        compressed: true,
        originalTurnCount: oldTurns.length,
        compressionRatio: compressedMessages.length / oldMessages.length,
        compressionTimestamp: Date.now(),
      },
      status: 'completed',
    };

    return [compressedTurn, ...recentTurns];
  }

  /**
   * Intelligent caching with LRU eviction
   */
  private addToCache(key: string, content: string): void {
    if (!this.config.enableCaching) {
      return;
    }

    const size = this.calculateSize(content);

    // Check if we need to evict entries
    this.evictIfNeeded(size);

    const entry: CacheEntry = {
      key,
      content,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      size,
    };

    this.cache.set(key, entry);
    this.currentCacheSize += size;

    logger.debug(`[PerformanceOptimizer] Added to cache: ${key} (${size} bytes)`);
  }

  /**
   * Get content from cache with LRU tracking
   */
  private getFromCache(key: string): string | null {
    if (!this.config.enableCaching) {
      return null;
    }

    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    // Check TTL
    if (Date.now() - entry.timestamp > this.config.cacheTTL) {
      this.cache.delete(key);
      this.currentCacheSize -= entry.size;
      return null;
    }

    // Update access tracking
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.content;
  }

  /**
   * Evict cache entries when needed (LRU policy)
   */
  private evictIfNeeded(newEntrySize: number): void {
    // Check size limit
    while (this.currentCacheSize + newEntrySize > this.config.maxCacheSize * 1024 * 1024) {
      this.evictLRU();
    }

    // Check entry count limit
    while (this.cache.size >= this.config.maxCacheEntries) {
      this.evictLRU();
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestEntry: CacheEntry | null = null;
    let oldestKey: string | null = null;

    for (const [key, entry] of this.cache.entries()) {
      if (!oldestEntry || entry.lastAccessed < oldestEntry.lastAccessed) {
        oldestEntry = entry;
        oldestKey = key;
      }
    }

    if (oldestKey && oldestEntry) {
      this.cache.delete(oldestKey);
      this.currentCacheSize -= oldestEntry.size;
      logger.debug(`[PerformanceOptimizer] Evicted from cache: ${oldestKey}`);
    }
  }

  /**
   * Summarization-based compression
   */
  private async summarizeConversation(
    messages: BaseMessage[],
    targetTokens: number
  ): Promise<BaseMessage[]> {
    if (!this.compressionModel) {
        logger.warning('[PerformanceOptimizer] No compression model available, falling back to token trimming');
      return this.trimByTokens(messages, targetTokens);
    }

    // Keep system messages and recent messages
    const systemMessages = messages.filter(m => m instanceof SystemMessage);
    const nonSystemMessages = messages.filter(m => !(m instanceof SystemMessage));

    if (nonSystemMessages.length <= 4) {
      return messages; // Too few messages to compress effectively
    }

    // Keep the last 2 exchanges and summarize the rest
    const recentMessages = nonSystemMessages.slice(-4);
    const messagesToSummarize = nonSystemMessages.slice(0, -4);

    try {
      // Create summarization prompt
      const conversationText = messagesToSummarize
        .map(msg => `${msg.constructor.name}: ${msg.content}`)
        .join('\n');

      const summaryPrompt = `Please provide a concise summary of the following conversation, preserving key information and context that would be important for continuing the conversation:

${conversationText}

Summary:`;

      const summaryResponse = await this.compressionModel.invoke([
        new HumanMessage({ content: summaryPrompt })
      ]);

      const summaryMessage = new SystemMessage({
        content: `Previous conversation summary: ${summaryResponse.content}`,
      });

      return [...systemMessages, summaryMessage, ...recentMessages];
    } catch (error) {
      logger.error('[PerformanceOptimizer] Summarization failed:', error);
      return this.trimByTokens(messages, targetTokens);
    }
  }

  /**
   * Token-based trimming compression
   */
  private trimByTokens(messages: BaseMessage[], targetTokens: number): BaseMessage[] {
    const systemMessages = messages.filter(m => m instanceof SystemMessage);
    const nonSystemMessages = messages.filter(m => !(m instanceof SystemMessage));

    let currentTokens = this.estimateTokens(systemMessages);
    const resultMessages = [...systemMessages];

    // Add messages from most recent backwards until we hit the limit
    for (let i = nonSystemMessages.length - 1; i >= 0; i--) {
      const message = nonSystemMessages[i];
      const messageTokens = this.estimateTokens([message]);

      if (currentTokens + messageTokens <= targetTokens) {
        resultMessages.push(message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    // Restore original order for non-system messages
    const finalNonSystemMessages = resultMessages
      .filter(m => !(m instanceof SystemMessage))
      .reverse();

    return [...systemMessages, ...finalNonSystemMessages];
  }

  /**
   * Semantic compression (placeholder for advanced implementation)
   */
  private async semanticCompression(
    messages: BaseMessage[],
    targetTokens: number
  ): Promise<BaseMessage[]> {
    // For now, fall back to summarization
    // In a full implementation, this would use semantic similarity
    // to identify and merge similar exchanges
    return this.summarizeConversation(messages, targetTokens);
  }

  /**
   * Estimate token count for messages
   * Uses character-based estimation as a fallback for unknown models
   */
  private estimateTokens(messages: BaseMessage[]): number {
    try {
      // Rough estimation: 1 token ≈ 4 characters for most models
      // This is a conservative estimate that works well for most use cases
      const totalChars = messages.reduce((sum, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content || '');
        return sum + content.length;
      }, 0);

      // Add some overhead for message structure and metadata
      const overhead = messages.length * 10; // ~10 tokens per message for structure

      return Math.ceil(totalChars / 4) + overhead;
    } catch (error) {
      logger.warning('[PerformanceOptimizer] Token estimation failed, using fallback:', error);
      // Ultra-conservative fallback
      return messages.length * 100; // Assume 100 tokens per message
    }
  }

  /**
   * Generate cache key for content
   */
  private generateCacheKey(content: BaseMessage[] | ConversationTurn[], type: string): string {
    const contentStr = JSON.stringify(content);
    // Simple hash function for demo purposes
    let hash = 0;
    for (let i = 0; i < contentStr.length; i++) {
      const char = contentStr.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `${type}_${hash.toString(36)}`;
  }

  /**
   * Calculate content size in bytes
   */
  private calculateSize(content: string): number {
    return new Blob([content]).size;
  }

  /**
   * Serialize messages for caching
   */
  private serializeMessages(messages: BaseMessage[]): string {
    return JSON.stringify(messages.map(msg => ({
      type: msg.constructor.name,
      content: msg.content,
    })));
  }

  /**
   * Parse messages from cache
   */
  private parseMessagesFromCache(cached: string): BaseMessage[] {
    try {
      const parsed = JSON.parse(cached);
      return parsed.map((msg: { type: string; content: string }) => {
        switch (msg.type) {
          case 'HumanMessage':
            return new HumanMessage({ content: msg.content });
          case 'AIMessage':
            return new AIMessage({ content: msg.content });
          case 'SystemMessage':
            return new SystemMessage({ content: msg.content });
          default:
            return new HumanMessage({ content: msg.content });
        }
      });
    } catch (error) {
      logger.error('[PerformanceOptimizer] Failed to parse cached messages:', error);
      return [];
    }
  }

  /**
   * Extract content from compressed messages
   */
  private extractCompressedContent(messages: BaseMessage[]): string {
    return messages
      .map(msg => typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content))
      .join('\n\n');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    entries: number;
    sizeBytes: number;
    sizeMB: number;
    hitRate: number;
  } {
    const totalAccesses = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.accessCount, 0);

    return {
      entries: this.cache.size,
      sizeBytes: this.currentCacheSize,
      sizeMB: Math.round((this.currentCacheSize / (1024 * 1024)) * 100) / 100,
      hitRate: totalAccesses > 0 ? this.cache.size / totalAccesses : 0,
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    this.currentCacheSize = 0;
    logger.info('[PerformanceOptimizer] Cache cleared');
  }

  /**
   * Optimize session for performance
   */
  async optimizeSession(
    sessionId: string,
    messages: BaseMessage[],
    turns: ConversationTurn[]
  ): Promise<{
    optimizedMessages: BaseMessage[];
    optimizedTurns: ConversationTurn[];
    compressionStats: {
      originalMessageCount: number;
      compressedMessageCount: number;
      originalTurnCount: number;
      compressedTurnCount: number;
      estimatedTokenSavings: number;
    };
  }> {
    const originalMessageTokens = this.estimateTokens(messages);

    // Compress messages if they exceed token limit
    const optimizedMessages = originalMessageTokens > this.config.tokenLimit
      ? await this.compressConversation(messages, sessionId)
      : messages;

    // Compress turns if needed
    const optimizedTurns = turns.length > 20
      ? await this.compressTurns(turns, sessionId)
      : turns;

    const compressedMessageTokens = this.estimateTokens(optimizedMessages);

    return {
      optimizedMessages,
      optimizedTurns,
      compressionStats: {
        originalMessageCount: messages.length,
        compressedMessageCount: optimizedMessages.length,
        originalTurnCount: turns.length,
        compressedTurnCount: optimizedTurns.length,
        estimatedTokenSavings: originalMessageTokens - compressedMessageTokens,
      },
    };
  }
}
