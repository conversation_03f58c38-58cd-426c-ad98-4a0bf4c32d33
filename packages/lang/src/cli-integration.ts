/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * CLI Integration for LangChain implementation
 * 
 * This module provides a drop-in replacement for the core implementation
 * that can be enabled via environment variable.
 */

import type { ConfigParameters } from '@google/gemini-cli-core';
import { createHaicodeAgent } from './index.js';
import logger from './utils/logger.js';

/**
 * Check if LangChain implementation should be used
 */
export const useHaiCode = process.env.GEMINI_CLI_USE_LANGCHAIN === 'true';

/**
 * Create a CLI instance based on environment settings
 * This function can be used as a drop-in replacement for the core Config class
 */
export async function createCLIInstance(params: ConfigParameters) {
  if (useHaiCode) {
    logger.info('[CLI] Using LangChain implementation');
    return await createHaicodeAgent(params);
  } else {
    // Fallback to core implementation
    const { Config } = await import('@google/gemini-cli-core');
    logger.info('[CLI] Using core implementation');
    return new Config(params);
  }
}

/**
 * Environment variable check utility
 */
export function getLangChainSettings() {
  return {
    enabled: useHaiCode,
    debug: process.env.GEMINI_CLI_LANGCHAIN_DEBUG === 'true',
    modelProvider: process.env.GEMINI_CLI_LANGCHAIN_PROVIDER || 'google',
  };
}

/**
 * Helper to set up LangChain environment
 */
export function setupLangChainEnvironment() {
  if (useHaiCode) {
    // Set debug level if enabled
    if (process.env.GEMINI_CLI_LANGCHAIN_DEBUG === 'true') {
      process.env.DEBUG = process.env.DEBUG 
        ? `${process.env.DEBUG},langchain*` 
        : 'langchain*';
    }
    
    // Additional LangChain-specific setup
    logger.success('[CLI] LangChain environment configured');
  }
}