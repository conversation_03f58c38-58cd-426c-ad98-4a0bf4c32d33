# @ht/hai-code-cli

基于 LangChain.js 与 LangGraph 的 AI Coding CLI 实现，提供与 `@google/gemini-cli` 核心包近似一致的能力，并适配 OpenAI-Compatible/Anthropic 等多模型供应商。

## 概述

这个包提供了与 `@google/gemini-cli` 核心包近似等价的替代实现，使用 LangChain.js 和 LangGraph 作为底层框架。主要特点：

- **LangChain 集成**: 使用 LangChain 的聊天模型、工具
- **LangGraph 状态管理**: 使用 StateGraph 实现 Agent 执行流程
- **工具兼容性**: 复用 core 包工具定义，支持 MCP 工具发现
- **会话管理与持久化**: 本地持久化（`~/.haicode/tmp/<project-hash>/sessions`）
- **可观测性**: 接入 Langfuse
- **类型安全**: TypeScript 支持

## 安装

```bash
npm install @ht/hai-code-cli -g
```

## 快速开始

### 使用

```bash
# 非交互模式
hai-code "Hello, how can you help me?"

# 交互模式
hai-code --interactive

# 指定模型（默认: ht::saas-deepseek-v3）
hai-code -m ht::saas-deepseek-v3 "Explain this code"

# 使用 OpenAI-Compatible 接口（例如本地/代理）
OPENAI_BASE_URL=http://localhost:11434/v1 OPENAI_API_KEY=ollama \
  hai-code -m ht::saas-deepseek-v3 "你好"
```


### 代码引入

```typescript
import { createHaicodeAgent } from '@ht/hai-code-cli';
import type { ConfigParameters } from '@google/gemini-cli-core';

const params: ConfigParameters = {
  sessionId: 'my-session',
  targetDir: '/path/to/project',
  debugMode: false,
  model: 'ht::saas-deepseek-v3',
  cwd: process.cwd(),
};

const cli = await createHaicodeAgent({
  ...params,
  baseURL: process.env.OPENAI_BASE_URL,
  authType: 'USE_OPENAI_COMPATIBLE',
  checkpointer: true,
});

// 处理消息
const response = await cli.processMessage('Hello, how can you help me?');
console.log(response);

// 流式处理
for await (const chunk of cli.streamMessage('Explain this code:')) {
  process.stdout.write(chunk);
}
```

## 主要组件

### LangChainConfig（配置）

配置管理器，管理认证、模型和工具设置：

```typescript
import { LangChainConfig } from '@ht/hai-code-cli';

const config = new LangChainConfig(params);
await config.initialize();

// 获取 LangChain 配置
const langChainConfig = config.getLangChainConfig();
```

### StateGraphAgent（Agent）

基于 LangGraph 的代理，处理消息和工具执行：

```typescript
import { StateGraphAgent, LangChainConfig } from '@ht/hai-code-cli';

// 直接使用底层 Agent（通常优先使用 createHaicodeAgent 封装）
const agent = new StateGraphAgent(new LangChainConfig({
  sessionId: 's1',
  targetDir: process.cwd(),
  model: 'ht::saas-deepseek-v3',
  cwd: process.cwd(),
  debugMode: false,
}) as unknown as any);

// 处理消息
const response = await agent.processMessage('Hello', 'session-id');

// 流式处理
for await (const chunk of agent.streamMessage('Hello', 'session-id')) {
  console.log(chunk);
}
```

### SessionManager

会话管理器，处理对话历史和记忆：

```typescript
import { SessionManager } from '@ht/hai-code-cli';

const sessionManager = new SessionManager();

// 创建会话
const sessionId = sessionManager.createSession('User memory');

// 获取会话
const session = sessionManager.getSession(sessionId);

// 更新会话
sessionManager.updateSession(sessionId, messages, 'Updated memory');
```

### LangChainContentGenerator

内容生成器，包装 LangChain 模型：

```typescript
import { LangChainContentGenerator } from '@ht/hai-code-cli';

const generator = new LangChainContentGenerator(chatModel, embeddings, tools);

// 生成内容
const response = await generator.generateContent(request);

// 流式生成
for await (const chunk of generator.generateContentStream(request)) {
  console.log(chunk);
}
```

## 工具系统

### CoreToolWrapper

将 core 包的工具适配到 LangChain 工具系统：

```typescript
import { CoreToolWrapper } from '@ht/hai-code-cli';

// 创建工具包装器
const wrapper = new CoreToolWrapper(coreTool, config);

// 在 LangChain 中使用
const modelWithTools = chatModel.bindTools([wrapper]);
```

### LangChainToolRegistry

工具注册表，管理所有可用工具：

```typescript
import { LangChainToolRegistry } from '@ht/hai-code-cli';

const registry = new LangChainToolRegistry(config);

// 注册工具
registry.registerCoreTool(coreTool);

// 获取 LangChain 工具
const tools = registry.getLangChainTools();
```

## 模型工厂

### createChatModel

根据认证类型创建适当的聊天模型：

```typescript
import { createChatModel } from '@ht/hai-code-cli';
import { AuthType } from '@google/gemini-cli-core';
import { ExtendedAuthType } from '@ht/hai-code-cli';

// Google Gemini / Vertex AI
const geminiModel = await createChatModel('gemini-1.5-flash', AuthType.USE_GEMINI);

// OpenAI-Compatible
const openaiCompatibleModel = await createChatModel(
  'ht::saas-deepseek-v3',
  ExtendedAuthType.USE_OPENAI_COMPATIBLE,
  process.env.OPENAI_API_KEY,
  undefined,
  undefined,
  process.env.OPENAI_BASE_URL,
);
```

### createEmbeddings

创建嵌入模型：

```typescript
import { createEmbeddings } from '@ht/hai-code-cli';
import { ExtendedAuthType } from '@ht/hai-code-cli';

const embeddings = await createEmbeddings(
  'ht::bge-embedding',
  ExtendedAuthType.USE_OPENAI_COMPATIBLE,
  process.env.OPENAI_API_KEY,
  undefined,
  undefined,
  process.env.OPENAI_BASE_URL,
);
```

## 开发

### 安装依赖

```bash
npm install
```

### 构建

```bash
npm run build
```

### 测试

```bash
npm test
```

### 代码检查

```bash
npm run lint
```

## 架构

```
src/
├── cli-integration.ts       # 与核心 CLI 的集成切换
├── config/                  # 配置
│   ├── config.ts            # LangChainConfig 实现
│   ├── constants.ts
│   ├── models.ts            # 供应商模型列表/默认配置
│   ├── mongo.ts             # Checkpointer（MongoDB）配置
│   └── langfuse.ts          # Langfuse 回调配置
├── core/                    # 核心组件
│   ├── stateGraphAgent.ts   # LangGraph StateGraph 代理
│   ├── contentGenerator.ts  # 内容生成器
│   ├── modelFactory.ts      # 模型工厂
│   └── sessionManager.ts    # 会话管理与持久化
├── tools/                   # 工具系统
│   ├── toolRegistry.ts      # 工具注册表（含 MCP 工具发现）
│   └── coreTools.ts         # 核心工具包装器工厂
├── types/                   # 类型定义
│   └── index.ts
└── index.ts                 # 主入口（导出 API / createHaicodeAgent）
```

## CLI 选项与环境变量

CLI（`hai-code`）主要选项：

- `-m, --model <model>`：指定模型（默认：`ht::saas-deepseek-v3`）
- `-p, --prompt <prompt>`：非交互模式直接处理单条 Prompt
- `-b, --base-url <url>`：OpenAI-Compatible Base URL（也可用 `OPENAI_BASE_URL`）
- `-i, --interactive`：交互模式
- `-d, --debug`：调试日志
- `-h, --help` / `-v, --version`

常用环境变量：

- `OPENAI_API_KEY` / `OPENAI_BASE_URL`
- `ANTHROPIC_API_KEY`
- `GEMINI_API_KEY` / `GOOGLE_CLOUD_PROJECT`
